#!/usr/bin/env python3
"""
Comprehensive validation script for production readiness.
Run this manually to validate the ailex-be-ingest pipeline.
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

def test_environment():
    """Test basic environment setup."""
    print("🔍 STEP 1: ENVIRONMENT VALIDATION")
    print("=" * 50)
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment loading successful")
        
        # Check critical environment variables
        required_vars = [
            'SUPABASE_URL', 'SUPABASE_KEY',
            'VOYAGE_API_KEY', 'PINECONE_API_KEY',
            'NEO4J_URI', 'NEO4J_USERNAME', 'NEO4J_PASSWORD'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing environment variables: {missing_vars}")
            return False
        else:
            print("✅ All required environment variables present")
            return True
            
    except Exception as e:
        print(f"❌ Environment test failed: {e}")
        return False

def test_database_connections():
    """Test connections to all databases."""
    print("\n🔍 STEP 2: DATABASE CONNECTION VALIDATION")
    print("=" * 50)
    
    success = True
    
    # Test Supabase
    try:
        from supabase import create_client
        supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
        response = supabase.table('global_registry').select('id').limit(1).execute()
        print("✅ Supabase connection successful")
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        success = False
    
    # Test Neo4j
    try:
        from neo4j import GraphDatabase
        driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'),
            auth=(os.getenv('NEO4J_USERNAME'), os.getenv('NEO4J_PASSWORD'))
        )
        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            test_value = result.single()['test']
            assert test_value == 1
        driver.close()
        print("✅ Neo4j connection successful")
    except Exception as e:
        print(f"❌ Neo4j connection failed: {e}")
        success = False
    
    # Test Pinecone
    try:
        from pinecone import Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index(os.getenv('PINECONE_INDEX'))
        stats = index.describe_index_stats()
        print("✅ Pinecone connection successful")
    except Exception as e:
        print(f"❌ Pinecone connection failed: {e}")
        success = False
    
    # Test Voyage AI
    try:
        import voyageai
        client = voyageai.Client(api_key=os.getenv('VOYAGE_API_KEY'))
        print("✅ Voyage AI connection successful")
    except Exception as e:
        print(f"❌ Voyage AI connection failed: {e}")
        success = False
    
    return success

def check_registry_state():
    """Check current registry state."""
    print("\n🔍 STEP 3: REGISTRY STATE CHECK")
    print("=" * 50)
    
    try:
        from supabase import create_client
        supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
        
        # Check all entries
        response = supabase.table('global_registry').select('*').execute()
        all_entries = response.data
        print(f"Total registry entries: {len(all_entries)}")
        
        # Check vlaamse entries
        response = supabase.table('global_registry').select('*').eq('source', 'vlaamse').execute()
        vlaamse_entries = response.data
        print(f"Vlaamse registry entries: {len(vlaamse_entries)}")
        
        if vlaamse_entries:
            print("Vlaamse entries:")
            for entry in vlaamse_entries[:5]:  # Show first 5
                print(f"  {entry['doc_id']} - neo4j:{entry['neo4j_loaded']}, pinecone:{entry['pinecone_loaded']}")
            if len(vlaamse_entries) > 5:
                print(f"  ... and {len(vlaamse_entries) - 5} more")
        
        # Check test entries
        response = supabase.table('global_registry').select('*').ilike('doc_id', '%test%').execute()
        test_entries = response.data
        print(f"Test registry entries: {len(test_entries)}")
        
        if test_entries:
            print("Test entries (should be cleaned):")
            for entry in test_entries:
                print(f"  {entry['source']}:{entry['doc_id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Registry check failed: {e}")
        return False

def check_database_state():
    """Check current database state."""
    print("\n🔍 STEP 4: DATABASE STATE CHECK")
    print("=" * 50)
    
    try:
        # Neo4j check
        from neo4j import GraphDatabase
        driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'),
            auth=(os.getenv('NEO4J_USERNAME'), os.getenv('NEO4J_PASSWORD'))
        )
        
        with driver.session() as session:
            # Count vlaamse acts
            result = session.run("MATCH (a:Act) WHERE a.id STARTS WITH 'vlaamse_' RETURN count(a) as count")
            vlaamse_acts = result.single()['count']
            
            # Count vlaamse articles
            result = session.run("MATCH (art:Article) WHERE art.id STARTS WITH 'vlaamse_' RETURN count(art) as count")
            vlaamse_articles = result.single()['count']
            
            # Count all acts
            result = session.run("MATCH (a:Act) RETURN count(a) as count")
            total_acts = result.single()['count']
            
            # Count all articles
            result = session.run("MATCH (art:Article) RETURN count(art) as count")
            total_articles = result.single()['count']
        
        driver.close()
        
        print(f"Neo4j state:")
        print(f"  Vlaamse Acts: {vlaamse_acts}")
        print(f"  Vlaamse Articles: {vlaamse_articles}")
        print(f"  Total Acts: {total_acts}")
        print(f"  Total Articles: {total_articles}")
        
        # Pinecone check
        from pinecone import Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index(os.getenv('PINECONE_INDEX'))
        stats = index.describe_index_stats()
        
        vlaamse_vectors = stats.namespaces.get('vlaamse_codex', {}).get('vector_count', 0)
        total_vectors = stats.total_vector_count
        
        print(f"Pinecone state:")
        print(f"  Vlaamse Vectors: {vlaamse_vectors}")
        print(f"  Total Vectors: {total_vectors}")
        
        return {
            'vlaamse_acts': vlaamse_acts,
            'vlaamse_articles': vlaamse_articles,
            'vlaamse_vectors': vlaamse_vectors,
            'total_acts': total_acts,
            'total_articles': total_articles,
            'total_vectors': total_vectors
        }
        
    except Exception as e:
        print(f"❌ Database state check failed: {e}")
        return None

def test_transactional_ingester():
    """Test TransactionalIngester with a simple document."""
    print("\n🔍 STEP 5: TRANSACTIONAL INGESTER TEST")
    print("=" * 50)
    
    try:
        from common.transaction import TransactionalIngester
        from common.models import CommonAct, CommonArticle
        from datetime import date
        import json
        
        # Create test data
        act = CommonAct(
            id='test_validation_001',
            title='Validation Test Act',
            date=date(2024, 1, 1),
            source='vlaamse',
            language='nl',
            metadata={'test': True}
        )
        
        articles = [
            CommonArticle(
                id='test_validation_001#art1',
                act_id='test_validation_001',
                number='1',
                heading='Validation Test Article',
                text='This is a validation test article with sufficient content to create embeddings and verify the TransactionalIngester functionality.',
                language='nl'
            )
        ]
        
        raw_content = json.dumps({'test': 'validation content'})
        
        print(f"Testing with:")
        print(f"  Act: {act.id}")
        print(f"  Articles: {len(articles)}")
        print(f"  Article text length: {len(articles[0].text)}")
        
        # Create TransactionalIngester
        ingester = TransactionalIngester(
            source='vlaamse',
            dry_run=False,
            save_raw_files=True,
            use_registry=True
        )
        
        # Test ingestion
        result = ingester.ingest_document_atomic(
            act=act,
            articles=articles,
            raw_content=raw_content,
            raw_metadata={'test': True}
        )
        
        print(f"TransactionalIngester result:")
        print(f"  success: {result.success}")
        print(f"  act_id: {result.act_id}")
        print(f"  articles_count: {result.articles_count}")
        print(f"  vectors_count: {result.vectors_count}")
        print(f"  files_saved: {result.files_saved}")
        print(f"  error: {result.error}")
        
        ingester.close()
        
        # Clean up test data
        print("Cleaning up test data...")
        
        # Clean Neo4j
        from neo4j import GraphDatabase
        driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'),
            auth=(os.getenv('NEO4J_USERNAME'), os.getenv('NEO4J_PASSWORD'))
        )
        with driver.session() as session:
            session.run("MATCH (a:Act) WHERE a.id = 'test_validation_001' DETACH DELETE a")
            session.run("MATCH (art:Article) WHERE art.id STARTS WITH 'test_validation_001#' DETACH DELETE art")
        driver.close()
        
        # Clean Pinecone
        from pinecone import Pinecone
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index(os.getenv('PINECONE_INDEX'))
        index.delete(filter={"act_id": "test_validation_001"}, namespace='vlaamse_codex')
        
        # Clean Registry
        from supabase import create_client
        supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
        supabase.table('global_registry').delete().eq('doc_id', 'test_validation_001').execute()
        
        print("✅ Test data cleaned up")
        
        # Evaluate result
        if result.success and result.vectors_count > 0 and result.files_saved > 0:
            print("✅ TransactionalIngester test PASSED")
            return True
        else:
            print("❌ TransactionalIngester test FAILED")
            return False
            
    except Exception as e:
        print(f"❌ TransactionalIngester test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run comprehensive validation."""
    print("🧪 AILEX-BE-INGEST PRODUCTION READINESS VALIDATION")
    print("=" * 60)
    print(f"Validation started at: {datetime.now()}")
    print()
    
    tests = [
        ("Environment Setup", test_environment),
        ("Database Connections", test_database_connections),
        ("Registry State", check_registry_state),
        ("Database State", check_database_state),
        ("TransactionalIngester", test_transactional_ingester)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Final assessment
    print("\n📊 VALIDATION RESULTS")
    print("=" * 60)
    
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 PRODUCTION READINESS: ✅ READY")
        print("All systems are functioning correctly!")
        print("The ailex-be-ingest pipeline is ready for production deployment.")
    else:
        print(f"\n⚠️  PRODUCTION READINESS: ❌ NOT READY")
        print(f"{total - passed} test(s) failed. Please address the issues before production deployment.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
