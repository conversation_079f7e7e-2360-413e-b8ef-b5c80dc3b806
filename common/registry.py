"""
Global Registry for ailex-be-ingest.

Provides CRUD operations for the Supabase registry table that tracks
the state of every legal document across all four stores.
"""

import hashlib
import logging
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from supabase import create_client, Client
from postgrest.exceptions import APIError

from .config import config

logger = logging.getLogger(__name__)


class ConsistencyStatus(Enum):
    """Consistency status values."""
    CONSISTENT = "CONSISTENT"
    NOT_LOADED = "NOT_LOADED"
    MISSING_VECTORS = "MISSING_VECTORS"
    MISSING_GRAPH = "MISSING_GRAPH"
    UNKNOWN = "UNKNOWN"


@dataclass
class RegistryEntry:
    """Registry entry data model."""
    id: Optional[str] = None
    source: str = ""
    doc_id: str = ""
    gcs_uri: Optional[str] = None
    sha256: Optional[str] = None
    neo4j_loaded: bool = False
    pinecone_loaded: bool = False
    article_count: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    consistency_status: Optional[ConsistencyStatus] = None


class GlobalRegistry:
    """
    Global registry client for tracking document state across all stores.
    """
    
    def __init__(self, supabase_url: Optional[str] = None, supabase_key: Optional[str] = None):
        """
        Initialize registry client.
        
        Args:
            supabase_url: Supabase project URL (defaults to config)
            supabase_key: Supabase API key (defaults to config)
        """
        self.supabase_url = supabase_url or config.SUPABASE_URL
        self.supabase_key = supabase_key or config.SUPABASE_KEY
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("Supabase URL and key must be provided via config or parameters")
        
        self.client: Client = create_client(self.supabase_url, self.supabase_key)
        self.table_name = "global_registry"
    
    def exists(self, source: str, doc_id: str, sha256: Optional[str] = None) -> bool:
        """
        Check if document exists in registry with matching checksum.
        
        Args:
            source: Source system identifier
            doc_id: Document identifier
            sha256: Optional SHA-256 checksum to verify
            
        Returns:
            True if document exists and is fully processed
        """
        try:
            print(f"🔍 Registry.exists() called for {source}:{doc_id}")
            query = self.client.table(self.table_name).select("*").eq("source", source).eq("doc_id", doc_id)

            response = query.execute()
            print(f"🔍 Registry query returned {len(response.data) if response.data else 0} entries")

            if not response.data:
                print(f"🔍 No registry entries found for {source}:{doc_id}")
                return False

            entry = response.data[0]
            print(f"🔍 Found registry entry: neo4j_loaded={entry.get('neo4j_loaded')}, pinecone_loaded={entry.get('pinecone_loaded')}")

            # If SHA-256 provided, verify it matches
            if sha256 and entry.get("sha256") != sha256:
                print(f"🔍 SHA-256 mismatch for {source}:{doc_id}")
                logger.info(f"Document {source}:{doc_id} exists but SHA-256 mismatch")
                return False

            # Check if fully processed
            result = entry.get("neo4j_loaded", False) and entry.get("pinecone_loaded", False)
            print(f"🔍 Registry exists result for {source}:{doc_id}: {result}")
            return result
            
        except APIError as e:
            logger.error(f"Failed to check registry existence: {e}")
            return False
    
    def upsert(self, entry: RegistryEntry) -> bool:
        """
        Upsert registry entry.
        
        Args:
            entry: Registry entry to upsert
            
        Returns:
            True if successful
        """
        try:
            data = {
                "source": entry.source,
                "doc_id": entry.doc_id,
                "gcs_uri": entry.gcs_uri,
                "sha256": entry.sha256,
                "neo4j_loaded": entry.neo4j_loaded,
                "pinecone_loaded": entry.pinecone_loaded,
                "article_count": entry.article_count
            }
            
            # Remove None values
            data = {k: v for k, v in data.items() if v is not None}
            
            response = self.client.table(self.table_name).upsert(
                data,
                on_conflict="source,doc_id"
            ).execute()
            
            if response.data:
                logger.debug(f"Upserted registry entry: {entry.source}:{entry.doc_id}")
                return True
            else:
                logger.error(f"Failed to upsert registry entry: {entry.source}:{entry.doc_id}")
                return False
                
        except APIError as e:
            logger.error(f"Failed to upsert registry entry: {e}")
            return False
    
    def get(self, source: str, doc_id: str) -> Optional[RegistryEntry]:
        """
        Get registry entry by source and doc_id.
        
        Args:
            source: Source system identifier
            doc_id: Document identifier
            
        Returns:
            RegistryEntry if found, None otherwise
        """
        try:
            response = self.client.table(self.table_name).select("*").eq("source", source).eq("doc_id", doc_id).execute()
            
            if response.data:
                return self._dict_to_entry(response.data[0])
            return None
            
        except APIError as e:
            logger.error(f"Failed to get registry entry: {e}")
            return None
    
    def list_entries(self, 
                    source: Optional[str] = None,
                    since: Optional[datetime] = None,
                    limit: int = 1000,
                    offset: int = 0) -> List[RegistryEntry]:
        """
        List registry entries with optional filters.
        
        Args:
            source: Filter by source system
            since: Filter by created_at >= since
            limit: Maximum number of entries to return
            offset: Number of entries to skip
            
        Returns:
            List of RegistryEntry objects
        """
        try:
            query = self.client.table(self.table_name).select("*")
            
            if source:
                query = query.eq("source", source)
            
            if since:
                query = query.gte("created_at", since.isoformat())
            
            query = query.range(offset, offset + limit - 1).order("created_at", desc=True)
            
            response = query.execute()
            
            return [self._dict_to_entry(row) for row in response.data]
            
        except APIError as e:
            logger.error(f"Failed to list registry entries: {e}")
            return []
    
    def get_consistency_stats(self, 
                            source: Optional[str] = None,
                            since: Optional[datetime] = None) -> Dict[str, int]:
        """
        Get consistency statistics.
        
        Args:
            source: Filter by source system
            since: Filter by created_at >= since
            
        Returns:
            Dictionary mapping status to count
        """
        try:
            # Use the database function for efficient stats
            params = {}
            if source:
                params['p_source'] = source
            if since:
                params['p_since'] = since.isoformat()
            
            response = self.client.rpc('get_consistency_stats', params).execute()
            
            return {row['status']: row['count'] for row in response.data}
            
        except APIError as e:
            logger.error(f"Failed to get consistency stats: {e}")
            return {}
    
    def get_inconsistent_entries(self, 
                                source: Optional[str] = None,
                                since: Optional[datetime] = None) -> List[RegistryEntry]:
        """
        Get entries that are not fully consistent.
        
        Args:
            source: Filter by source system
            since: Filter by created_at >= since
            
        Returns:
            List of inconsistent RegistryEntry objects
        """
        try:
            query = self.client.table("registry_consistency_view").select("*").neq("consistency_status", "CONSISTENT")
            
            if source:
                query = query.eq("source", source)
            
            if since:
                query = query.gte("created_at", since.isoformat())
            
            response = query.execute()
            
            return [self._dict_to_entry(row) for row in response.data]
            
        except APIError as e:
            logger.error(f"Failed to get inconsistent entries: {e}")
            return []
    
    def update_flags(self, 
                    source: str, 
                    doc_id: str,
                    neo4j_loaded: Optional[bool] = None,
                    pinecone_loaded: Optional[bool] = None,
                    article_count: Optional[int] = None) -> bool:
        """
        Update specific flags for a registry entry.
        
        Args:
            source: Source system identifier
            doc_id: Document identifier
            neo4j_loaded: Neo4j loaded status
            pinecone_loaded: Pinecone loaded status
            article_count: Article count
            
        Returns:
            True if successful
        """
        try:
            data = {}
            if neo4j_loaded is not None:
                data["neo4j_loaded"] = neo4j_loaded
            if pinecone_loaded is not None:
                data["pinecone_loaded"] = pinecone_loaded
            if article_count is not None:
                data["article_count"] = article_count
            
            if not data:
                return True  # Nothing to update
            
            response = self.client.table(self.table_name).update(data).eq("source", source).eq("doc_id", doc_id).execute()
            
            return len(response.data) > 0
            
        except APIError as e:
            logger.error(f"Failed to update flags: {e}")
            return False
    
    def delete(self, source: str, doc_id: str) -> bool:
        """
        Delete registry entry.
        
        Args:
            source: Source system identifier
            doc_id: Document identifier
            
        Returns:
            True if successful
        """
        try:
            response = self.client.table(self.table_name).delete().eq("source", source).eq("doc_id", doc_id).execute()
            
            return len(response.data) > 0
            
        except APIError as e:
            logger.error(f"Failed to delete registry entry: {e}")
            return False
    
    def _dict_to_entry(self, data: Dict[str, Any]) -> RegistryEntry:
        """Convert dictionary to RegistryEntry."""
        # Parse timestamps
        def parse_timestamp(timestamp_str):
            """Parse timestamp with robust microseconds handling."""
            if not timestamp_str:
                return None
            try:
                # Replace Z with +00:00
                ts = timestamp_str.replace("Z", "+00:00")

                # Handle microseconds precision - truncate to 6 digits max
                if "." in ts:
                    before_dot, after_dot = ts.split(".", 1)
                    if "+" in after_dot:
                        microseconds, timezone = after_dot.split("+", 1)
                        microseconds = microseconds[:6]  # Truncate to 6 digits
                        ts = f"{before_dot}.{microseconds}+{timezone}"
                    elif "-" in after_dot:
                        microseconds, timezone = after_dot.rsplit("-", 1)
                        microseconds = microseconds[:6]  # Truncate to 6 digits
                        ts = f"{before_dot}.{microseconds}-{timezone}"

                return datetime.fromisoformat(ts)
            except Exception as e:
                logger.warning(f"Failed to parse timestamp '{timestamp_str}': {e}")
                return None

        created_at = parse_timestamp(data.get("created_at"))
        updated_at = parse_timestamp(data.get("updated_at"))
        
        # Parse consistency status
        consistency_status = None
        if data.get("consistency_status"):
            try:
                consistency_status = ConsistencyStatus(data["consistency_status"])
            except ValueError:
                pass
        
        return RegistryEntry(
            id=data.get("id"),
            source=data.get("source", ""),
            doc_id=data.get("doc_id", ""),
            gcs_uri=data.get("gcs_uri"),
            sha256=data.get("sha256"),
            neo4j_loaded=data.get("neo4j_loaded", False),
            pinecone_loaded=data.get("pinecone_loaded", False),
            article_count=data.get("article_count", 0),
            created_at=created_at,
            updated_at=updated_at,
            consistency_status=consistency_status
        )


def calculate_sha256(content: str) -> str:
    """Calculate SHA-256 hash of content."""
    return hashlib.sha256(content.encode('utf-8')).hexdigest()


def create_gcs_uri(source: str, act_id: str, year: int, extension: str = "xml") -> str:
    """Create GCS URI for a document."""
    bucket = config.GCS_BUCKET or "ailex-be"
    return f"gs://{bucket}/raw/{source}/{year}/{act_id}.{extension}"


# Convenience functions
def get_registry() -> GlobalRegistry:
    """Get a registry instance with default configuration."""
    return GlobalRegistry()


def registry_exists(source: str, doc_id: str, sha256: Optional[str] = None) -> bool:
    """Check if document exists in registry."""
    registry = get_registry()
    return registry.exists(source, doc_id, sha256)
