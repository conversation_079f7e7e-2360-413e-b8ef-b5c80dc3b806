"""
Transactional ingestion patterns for ailex-be-ingest.

Provides atomic per-document ingestion with proper rollback on failure.
Ensures either a document is fully processed or nothing is committed.
"""

import json
import logging
from typing import List, Dict, Any, Optional, Callable
from contextlib import contextmanager
from dataclasses import dataclass

import tiktoken
import voyageai
from pinecone import Pinecone
from google.cloud import storage
from neo4j import Session

from .models import CommonAct, CommonArticle, create_vector_id, PINECONE_NAMESPACES
from .neo4j import Neo4jClient
from .config import config
from .checkpoint import Checkpoint
from .registry import GlobalRegistry, RegistryEntry, calculate_sha256, create_gcs_uri
from .integrity import DataIntegrityValidator, IntegrityLevel

logger = logging.getLogger(__name__)


@dataclass
class IngestionResult:
    """Result of document ingestion."""
    success: bool
    act_id: str
    articles_count: int = 0
    vectors_count: int = 0
    files_saved: int = 0
    error: Optional[str] = None


class TransactionalIngester:
    """
    Atomic document ingester with rollback capabilities.
    
    Ensures either complete success or complete rollback for each document.
    """
    
    def __init__(self,
                 source: str,
                 dry_run: bool = False,
                 save_raw_files: bool = True,
                 use_registry: bool = True):
        """
        Initialize transactional ingester.

        Args:
            source: Source identifier (vlaamse, eu_dump, eu_rest)
            dry_run: If True, don't write to external services
            save_raw_files: Whether to save raw files to GCS
            use_registry: Whether to use global registry for tracking
        """
        self.source = source
        self.dry_run = dry_run
        self.save_raw_files = save_raw_files
        self.use_registry = use_registry

        # Initialize tokenizer
        self.tokenizer = tiktoken.get_encoding("cl100k_base")

        # Initialize services (only if not dry run)
        if not dry_run:
            self._setup_services()

        # Initialize integrity validator
        self.integrity_validator = DataIntegrityValidator() if not dry_run else None
    
    def _setup_services(self):
        """Initialize external services."""
        # Voyage AI for embeddings
        self.voyage_client = voyageai.Client(api_key=config.VOYAGE_API_KEY)
        
        # Pinecone for vector storage
        pc = Pinecone(api_key=config.PINECONE_API_KEY)
        self.pinecone_index = pc.Index(config.PINECONE_INDEX)
        
        # Neo4j for knowledge graph
        self.neo4j_client = Neo4jClient()
        
        # Google Cloud Storage for raw files
        if self.save_raw_files and config.GCS_BUCKET:
            self.gcs_client = storage.Client()
            self.gcs_bucket = self.gcs_client.bucket(config.GCS_BUCKET)
        else:
            self.gcs_client = None
            self.gcs_bucket = None

        # Global Registry for tracking
        if self.use_registry:
            try:
                self.registry = GlobalRegistry()
            except Exception as e:
                logger.warning(f"Failed to initialize registry: {e}")
                self.registry = None
        else:
            self.registry = None

    @staticmethod
    def _serialize_metadata(metadata: dict) -> str:
        """
        Serialize metadata dictionary to JSON string for Neo4j storage.

        Args:
            metadata: Dictionary to serialize

        Returns:
            JSON string representation
        """
        if not metadata:
            return "{}"
        return json.dumps(metadata, default=str, ensure_ascii=False)

    def ingest_document_atomic(self,
                              act: CommonAct,
                              articles: List[CommonArticle],
                              raw_content: Optional[str] = None,
                              raw_metadata: Optional[Dict[str, Any]] = None) -> IngestionResult:
        """
        Atomically ingest a single document with registry tracking.

        Either all operations succeed or all are rolled back.

        Args:
            act: CommonAct instance
            articles: List of CommonArticle instances
            raw_content: Raw file content (XML, etc.)
            raw_metadata: Additional metadata for raw file storage

        Returns:
            IngestionResult with success status and counts
        """
        print(f"🔍 TransactionalIngester.ingest_document_atomic called for {act.id}")
        print(f"🔍 Config: dry_run={self.dry_run}, save_raw_files={self.save_raw_files}, use_registry={self.use_registry}")
        print(f"🔍 Input: {len(articles)} articles, raw_content_length={len(raw_content) if raw_content else 0}")

        if self.dry_run:
            print(f"🔍 Running in DRY_RUN mode for {act.id}")
            return self._dry_run_ingest(act, articles)

        print(f"🔍 Starting atomic ingestion for {act.id}")

        # Merge short articles with adjacent ones to ensure all content is vectorized
        articles = self._merge_short_articles(articles)
        logger.debug(f"After merging short articles: {len(articles)} articles for {act.id}")

        # Calculate SHA-256 if raw content provided
        sha256 = None
        if raw_content:
            sha256 = calculate_sha256(raw_content)
            print(f"🔍 SHA-256 calculated for {act.id}: {sha256[:16]}...")
        else:
            print(f"🔍 No raw content provided for {act.id}, SHA-256 is None")

        # Atomic registry reservation - FIXED RACE CONDITION
        print(f"🔍 Registry check: registry={self.registry is not None}, sha256={sha256 is not None}")
        if self.registry:
            import uuid
            processing_id = str(uuid.uuid4())
            print(f"🔍 Attempting to reserve {act.id} for processing with ID {processing_id}")

            # Try to atomically reserve the document
            if not self.registry.try_reserve_document(self.source, act.id, sha256, processing_id):
                print(f"⚠️  Document {act.id} already exists or is being processed (registry check)")
                logger.info(f"Document {act.id} already exists or is being processed (registry check)")

                # Get the existing registry entry to return accurate counts
                try:
                    response = self.registry.client.table(self.registry.table_name).select("*").eq("source", self.source).eq("doc_id", act.id).execute()
                    if response.data:
                        entry = response.data[0]
                        existing_vectors = entry.get("article_count", 0)  # Use article_count as proxy for vectors
                        existing_files = 1 if entry.get("gcs_uri") else 0
                        print(f"✅ Returning existing counts: vectors={existing_vectors}, files={existing_files}")
                        return IngestionResult(
                            success=True,
                            act_id=act.id,
                            articles_count=len(articles),
                            vectors_count=existing_vectors,
                            files_saved=existing_files
                        )
                except Exception as e:
                    print(f"⚠️  Could not get existing counts, using defaults: {e}")

                # Fallback to default counts
                return IngestionResult(
                    success=True,
                    act_id=act.id,
                    articles_count=len(articles),
                    vectors_count=0,  # Conservative default
                    files_saved=0
                )
            else:
                print(f"✅ Document {act.id} successfully reserved for processing")
                # Store processing_id for later use in status updates
                self._current_processing_id = processing_id
        else:
            print(f"⚠️  Registry disabled for {act.id}")
            self._current_processing_id = None

        try:
            # Use Neo4j transaction for atomicity
            with self.neo4j_client.driver.session() as session:
                with session.begin_transaction() as tx:
                    result = self._ingest_with_transaction(
                        tx, act, articles, raw_content, raw_metadata, sha256
                    )

                    if result.success:
                        tx.commit()
                        logger.info(f"Successfully ingested {act.id}: {result.articles_count} articles, {result.vectors_count} vectors")
                    else:
                        tx.rollback()
                        logger.error(f"Ingestion failed for {act.id}: {result.error}")

                    return result

        except Exception as e:
            logger.error(f"Atomic ingestion failed for {act.id}: {e}")
            return IngestionResult(
                success=False,
                act_id=act.id,
                error=str(e)
            )
    
    def _ingest_with_transaction(self,
                               tx: Session,
                               act: CommonAct,
                               articles: List[CommonArticle],
                               raw_content: Optional[str],
                               raw_metadata: Optional[Dict[str, Any]],
                               sha256: Optional[str]) -> IngestionResult:
        """Perform ingestion within a Neo4j transaction."""

        try:
            print(f"🔍 STEP 1: Updating registry status to PROCESSING for {act.id}")
            # Step 1: Update registry status to PROCESSING (document already reserved)
            if self.registry and hasattr(self, '_current_processing_id'):
                # Update status to PROCESSING
                if not self.registry.update_processing_status(
                    self.source, act.id, "PROCESSING", self._current_processing_id
                ):
                    raise Exception("Failed to update registry status to PROCESSING")
                print(f"✅ STEP 1 SUCCESS: Registry status updated to PROCESSING for {act.id}")

                # Update additional fields if needed
                gcs_uri = None
                if self.save_raw_files and raw_content:
                    gcs_uri = create_gcs_uri(self.source, act.id, act.date.year)

                # Update the registry entry with additional details
                registry_entry = RegistryEntry(
                    source=self.source,
                    doc_id=act.id,
                    gcs_uri=gcs_uri,
                    sha256=sha256,
                    neo4j_loaded=False,
                    pinecone_loaded=False,
                    article_count=len(articles),
                    processing_status="PROCESSING",
                    processing_id=self._current_processing_id
                )

                if not self.registry.upsert(registry_entry):
                    raise Exception("Failed to update registry entry details")
            else:
                print(f"⚠️  STEP 1 SKIPPED: Registry disabled or no processing ID for {act.id}")

            print(f"🔍 STEP 2: Saving raw files for {act.id}")
            # Step 2: Save raw files to GCS (if enabled)
            files_saved = 0
            if self.save_raw_files and raw_content:
                self._save_raw_files(act, raw_content, raw_metadata)
                files_saved = 1
                print(f"✅ STEP 2 SUCCESS: Raw files saved for {act.id}")
            else:
                print(f"⚠️  STEP 2 SKIPPED: Raw file saving disabled or no content for {act.id}")

            print(f"🔍 STEP 3: Upserting act in Neo4j for {act.id}")
            # Step 3: Upsert act in Neo4j (within transaction)
            self._upsert_act_tx(tx, act)
            print(f"✅ STEP 3 SUCCESS: Act upserted for {act.id}")

            print(f"🔍 STEP 4: Upserting {len(articles)} articles in Neo4j for {act.id}")
            # Step 4: Upsert articles in Neo4j (within transaction)
            for article in articles:
                self._upsert_article_tx(tx, article)
            print(f"✅ STEP 4 SUCCESS: {len(articles)} articles upserted for {act.id}")

            print(f"🔍 STEP 5: Updating registry Neo4j flag for {act.id}")
            # Step 5: Update registry Neo4j flag
            if self.registry:
                if not self.registry.update_flags(self.source, act.id, neo4j_loaded=True):
                    raise Exception("Failed to update registry Neo4j flag")
                print(f"✅ STEP 5 SUCCESS: Registry Neo4j flag updated for {act.id}")
            else:
                print(f"⚠️  STEP 5 SKIPPED: Registry disabled for {act.id}")

            print(f"🔍 STEP 6: Creating and upserting vectors for {act.id}")
            # Step 6: Create and upsert vectors to Pinecone
            vectors_count = self._create_and_upsert_vectors(act, articles)
            print(f"✅ STEP 6 SUCCESS: {vectors_count} vectors created for {act.id}")

            print(f"🔍 STEP 7: Updating registry Pinecone flag for {act.id}")
            # Step 7: Update registry Pinecone flag (final step)
            if self.registry:
                if not self.registry.update_flags(self.source, act.id, pinecone_loaded=True, article_count=vectors_count):
                    raise Exception("Failed to update registry Pinecone flag")
                print(f"✅ STEP 7 SUCCESS: Registry Pinecone flag updated for {act.id}")
            else:
                print(f"⚠️  STEP 7 SKIPPED: Registry disabled for {act.id}")

            print(f"🔍 STEP 8: Marking document as COMPLETED in registry for {act.id}")
            # Step 8: Mark document as completed in registry
            if self.registry and hasattr(self, '_current_processing_id'):
                if not self.registry.update_processing_status(
                    self.source, act.id, "COMPLETED", self._current_processing_id
                ):
                    logger.warning(f"Failed to update registry status to COMPLETED for {act.id}")
                    # Don't fail the transaction for this
                else:
                    print(f"✅ STEP 8 SUCCESS: Registry status updated to COMPLETED for {act.id}")
            else:
                print(f"⚠️  STEP 8 SKIPPED: Registry disabled or no processing ID for {act.id}")

            # Step 9: Validate data integrity (optional but recommended)
            if self.integrity_validator:
                integrity_issues = self.integrity_validator.validate_cross_system_consistency(act.id)
                critical_issues = [i for i in integrity_issues if i.level == IntegrityLevel.CRITICAL]
                if critical_issues:
                    logger.warning(f"Critical integrity issues found for {act.id}: {[i.description for i in critical_issues]}")
                    # Note: We don't fail here to avoid rollback, but log for monitoring

            return IngestionResult(
                success=True,
                act_id=act.id,
                articles_count=len(articles),
                vectors_count=vectors_count,
                files_saved=files_saved
            )

        except Exception as e:
            print(f"❌ TRANSACTION EXCEPTION: {e}")
            logger.error(f"Transaction step failed: {e}")
            import traceback
            traceback.print_exc()

            # Mark document as FAILED in registry
            if self.registry and hasattr(self, '_current_processing_id'):
                try:
                    self.registry.update_processing_status(
                        self.source, act.id, "FAILED", self._current_processing_id
                    )
                    print(f"⚠️  Registry status updated to FAILED for {act.id}")
                except Exception as registry_error:
                    logger.error(f"Failed to update registry status to FAILED: {registry_error}")

            raise  # Will trigger rollback
    
    def _save_raw_files(self, act: CommonAct, content: str, metadata: Optional[Dict[str, Any]]):
        """Save raw files to Google Cloud Storage."""
        print(f"🔍 _save_raw_files called for {act.id}, gcs_bucket={self.gcs_bucket is not None}")

        if not self.gcs_bucket:
            print(f"❌ GCS bucket not configured for {act.id}, skipping file save")
            return
        
        try:
            # Create file path: raw/{source}/{year}/{act_id}.{ext}
            year = act.date.year
            file_extension = self._get_file_extension(metadata)
            file_path = f"raw/{self.source}/{year}/{act.id}.{file_extension}"
            
            # Upload content
            blob = self.gcs_bucket.blob(file_path)
            blob.upload_from_string(content, content_type=self._get_content_type(file_extension))
            
            logger.info(f"Saved raw file to GCS: {file_path}")
            
            # Also save metadata if provided
            if metadata:
                metadata_path = f"raw/{self.source}/{year}/{act.id}.metadata.json"
                metadata_blob = self.gcs_bucket.blob(metadata_path)
                import json
                metadata_blob.upload_from_string(
                    json.dumps(metadata, indent=2, default=str),
                    content_type='application/json'
                )
        
        except Exception as e:
            logger.error(f"Failed to save raw files for {act.id}: {e}")
            raise
    
    def _upsert_act_tx(self, tx: Session, act: CommonAct):
        """Upsert act within Neo4j transaction."""
        from .models import NEO4J_LABELS
        
        query = f"""
        MERGE (a:{NEO4J_LABELS['ACT']} {{id: $id}})
        SET a.title = $title,
            a.date = $date,
            a.language = $language,
            a.source = $source,
            a.eli = $eli,
            a.metadata = $metadata,
            a.updated_at = datetime()
        RETURN a.id as id
        """
        
        result = tx.run(query, {
            "id": act.id,
            "title": act.title,
            "date": act.date.isoformat(),
            "language": act.language,
            "source": act.source,
            "eli": act.eli,
            "metadata": self._serialize_metadata(act.metadata)
        })
        
        if not result.single():
            raise Exception(f"Failed to upsert act {act.id}")
    
    def _upsert_article_tx(self, tx: Session, article: CommonArticle):
        """Upsert article within Neo4j transaction."""
        from .models import NEO4J_LABELS, NEO4J_RELATIONSHIPS
        
        query = f"""
        MERGE (art:{NEO4J_LABELS['ARTICLE']} {{id: $id}})
        SET art.act_id = $act_id,
            art.number = $number,
            art.heading = $heading,
            art.text = $text,
            art.language = $language,
            art.metadata = $metadata,
            art.updated_at = datetime()
        
        WITH art
        MATCH (act:{NEO4J_LABELS['ACT']} {{id: $act_id}})
        MERGE (act)-[:{NEO4J_RELATIONSHIPS['HAS_ARTICLE']}]->(art)
        
        RETURN art.id as id
        """
        
        result = tx.run(query, {
            "id": article.id,
            "act_id": article.act_id,
            "number": article.number,
            "heading": article.heading,
            "text": article.text,
            "language": article.language,
            "metadata": self._serialize_metadata(article.metadata)
        })
        
        if not result.single():
            raise Exception(f"Failed to upsert article {article.id}")
    
    def _create_and_upsert_vectors(self, act: CommonAct, articles: List[CommonArticle]) -> int:
        """Create embeddings and upsert to Pinecone with deterministic IDs."""
        print(f"🔍 _create_and_upsert_vectors called for {act.id} with {len(articles)} articles")

        if not articles:
            print(f"❌ No articles provided for {act.id}")
            logger.warning(f"No articles provided for {act.id}")
            return 0

        try:
            # Chunk article texts
            all_chunks = []
            chunk_metadata = []

            # Debug: Log article content
            logger.debug(f"Processing {len(articles)} articles for {act.id}")
            for i, article in enumerate(articles):
                text_length = len(article.text) if article.text else 0
                logger.debug(f"  Article {i+1} ({article.number}): {text_length} chars")
                if text_length == 0:
                    logger.warning(f"  Article {article.number} has no text content")
            
            for article in articles:
                chunks = self._chunk_text(article.text)
                for chunk_index, chunk in enumerate(chunks):
                    # Create deterministic vector ID
                    vector_id = create_vector_id(
                        source=self.source,
                        act_id=act.id,
                        article_id=article.id,
                        chunk_index=chunk_index
                    )
                    
                    all_chunks.append(chunk)
                    # Create metadata, filtering out None values for Pinecone
                    metadata = {
                        'vector_id': vector_id,
                        'act_id': act.id,
                        'article_id': article.id,
                        'article_number': article.number,
                        'chunk_index': chunk_index,
                        'language': article.language,
                        'source': act.source,
                        'title': act.title,
                        'date': act.date.isoformat(),
                        'text_preview': chunk[:200]
                    }

                    # Only add ELI if it's not None
                    if act.eli:
                        metadata['eli'] = act.eli

                    chunk_metadata.append(metadata)
            
            if not all_chunks:
                logger.warning(f"No text chunks created for {act.id} - all articles have insufficient content")
                return 0
            
            # Create embeddings
            logger.info(f"Creating embeddings for {len(all_chunks)} chunks using Voyage AI")
            try:
                result = self.voyage_client.embed(
                    texts=all_chunks,
                    model=config.VOYAGE_MODEL,
                    output_dimension=config.VOYAGE_DIMENSION
                )
                logger.info(f"Successfully created {len(result.embeddings)} embeddings")
            except Exception as e:
                logger.error(f"Failed to create embeddings: {e}")
                raise
            
            # Prepare vectors for Pinecone with deterministic IDs
            vectors = []
            namespace = PINECONE_NAMESPACES[act.source]
            
            for chunk, embedding, metadata in zip(all_chunks, result.embeddings, chunk_metadata):
                vectors.append({
                    'id': metadata['vector_id'],  # Deterministic ID
                    'values': embedding,
                    'metadata': {k: v for k, v in metadata.items() if k != 'vector_id'}
                })
            
            # Upsert to Pinecone (idempotent)
            logger.info(f"Upserting {len(vectors)} vectors to Pinecone namespace '{namespace}'")
            try:
                self.pinecone_index.upsert(vectors=vectors, namespace=namespace)
                logger.info(f"Successfully upserted {len(vectors)} vectors for {act.id}")
            except Exception as e:
                logger.error(f"Failed to upsert vectors to Pinecone: {e}")
                raise

            return len(vectors)
        
        except Exception as e:
            logger.error(f"Failed to create vectors for {act.id}: {e}")
            raise
    
    def _chunk_text(self, text: str) -> List[str]:
        """Split text into overlapping chunks."""
        if not text or len(text.strip()) < 10:  # More lenient minimum
            return []

        # For very short text, return as single chunk
        if len(text.strip()) < 100:
            return [text.strip()]

        tokens = self.tokenizer.encode(text)
        chunks = []
        chunk_size = config.CHUNK_SIZE
        overlap = config.CHUNK_OVERLAP

        for i in range(0, len(tokens), chunk_size - overlap):
            chunk_tokens = tokens[i:i + chunk_size]
            if len(chunk_tokens) < 10:  # More lenient minimum for chunks
                continue
            chunk_text = self.tokenizer.decode(chunk_tokens)
            chunks.append(chunk_text)

        return chunks

    def _merge_short_articles(self, articles: List[CommonArticle], min_length: int = 100) -> List[CommonArticle]:
        """
        Merge short articles with adjacent ones to ensure all content is vectorized.

        Strategy: Merge short articles with the next substantial article, or if at end,
        with the previous substantial article.
        """
        if not articles:
            return articles

        merged_articles = []
        i = 0

        while i < len(articles):
            current_article = articles[i]

            # Check if current article is too short
            if len(current_article.text.strip()) < min_length:
                # Try to merge with next article
                if i + 1 < len(articles):
                    next_article = articles[i + 1]
                    # Create merged article with next article's ID as primary
                    merged_text = f"{current_article.text.strip()}\n\n{next_article.text.strip()}"
                    merged_article = CommonArticle(
                        id=next_article.id,  # Use next article's ID
                        number=f"{current_article.number}-{next_article.number}",  # Combined numbering
                        text=merged_text,
                        act_id=next_article.act_id,
                        language=next_article.language,
                        heading=next_article.heading,
                        metadata={
                            **next_article.metadata,
                            "merged_from": [current_article.number, next_article.number],
                            "original_articles": [current_article.id, next_article.id]
                        }
                    )
                    merged_articles.append(merged_article)
                    i += 2  # Skip both articles
                    continue

                # If at end, merge with previous article (if exists and not already processed)
                elif merged_articles:
                    prev_article = merged_articles[-1]
                    merged_text = f"{prev_article.text.strip()}\n\n{current_article.text.strip()}"
                    # Update the previous article in place
                    merged_articles[-1] = CommonArticle(
                        id=prev_article.id,
                        number=f"{prev_article.number}-{current_article.number}",
                        text=merged_text,
                        act_id=prev_article.act_id,
                        language=prev_article.language,
                        heading=prev_article.heading,
                        metadata={
                            **prev_article.metadata,
                            "merged_from": prev_article.metadata.get("merged_from", [prev_article.number]) + [current_article.number],
                            "original_articles": prev_article.metadata.get("original_articles", [prev_article.id]) + [current_article.id]
                        }
                    )
                    i += 1
                    continue

                # If it's the only article and short, keep it as-is (will be handled by chunking)
                else:
                    merged_articles.append(current_article)
                    i += 1
                    continue

            # Article is substantial enough, keep as-is
            merged_articles.append(current_article)
            i += 1

        return merged_articles
    
    def _dry_run_ingest(self, act: CommonAct, articles: List[CommonArticle]) -> IngestionResult:
        """Simulate ingestion for dry run."""
        logger.info(f"DRY RUN: Would ingest {act.id} with {len(articles)} articles")
        
        # Simulate chunking to get vector count
        total_chunks = 0
        for article in articles:
            chunks = self._chunk_text(article.text)
            total_chunks += len(chunks)
        
        return IngestionResult(
            success=True,
            act_id=act.id,
            articles_count=len(articles),
            vectors_count=total_chunks,
            files_saved=1 if self.save_raw_files else 0
        )
    
    def _get_file_extension(self, metadata: Optional[Dict[str, Any]]) -> str:
        """Get appropriate file extension based on metadata."""
        if not metadata:
            return "xml"
        
        # Check content type or URL for extension hints
        content_type = metadata.get('content_type', '')
        url = metadata.get('url', '')
        
        if 'xml' in content_type.lower() or url.endswith('.xml'):
            return 'xml'
        elif 'pdf' in content_type.lower() or url.endswith('.pdf'):
            return 'pdf'
        else:
            return 'xml'  # Default
    
    def _get_content_type(self, extension: str) -> str:
        """Get MIME type for file extension."""
        content_types = {
            'xml': 'application/xml',
            'pdf': 'application/pdf',
            'json': 'application/json',
            'txt': 'text/plain'
        }
        return content_types.get(extension, 'application/octet-stream')
    
    def close(self):
        """Clean up resources."""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
