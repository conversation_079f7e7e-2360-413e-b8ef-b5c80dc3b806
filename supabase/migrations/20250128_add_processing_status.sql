-- Migration to add processing status fields to existing global_registry table
-- This migration adds the new columns needed for proper deduplication and state management

-- Add new columns for processing state management
ALTER TABLE public.global_registry 
ADD COLUMN IF NOT EXISTS processing_status TEXT DEFAULT 'NOT_STARTED' CHECK (processing_status IN ('NOT_STARTED', 'RESERVED', 'PROCESSING', 'COMPLETED', 'FAILED'));

ALTER TABLE public.global_registry 
ADD COLUMN IF NOT EXISTS processing_id TEXT;

ALTER TABLE public.global_registry 
ADD COLUMN IF NOT EXISTS reserved_at TIMESTAMPTZ;

-- Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_registry_processing_status ON public.global_registry (processing_status);
CREATE INDEX IF NOT EXISTS idx_registry_processing_id ON public.global_registry (processing_id);
CREATE INDEX IF NOT EXISTS idx_registry_reserved_at ON public.global_registry (reserved_at);

-- Update existing entries to have proper processing_status based on their current state
UPDATE public.global_registry 
SET processing_status = CASE 
    WHEN neo4j_loaded = true AND pinecone_loaded = true THEN 'COMPLETED'
    WHEN neo4j_loaded = false AND pinecone_loaded = false THEN 'NOT_STARTED'
    ELSE 'FAILED'  -- Partial processing indicates a failure
END
WHERE processing_status = 'NOT_STARTED';

-- Update the consistency view to include new fields
CREATE OR REPLACE VIEW public.registry_consistency_view AS
SELECT 
    id,
    source,
    doc_id,
    gcs_uri,
    sha256,
    neo4j_loaded,
    pinecone_loaded,
    article_count,
    processing_status,
    processing_id,
    reserved_at,
    created_at,
    updated_at,
    CASE 
        WHEN processing_status = 'COMPLETED' AND neo4j_loaded AND pinecone_loaded THEN 'CONSISTENT'
        WHEN processing_status IN ('RESERVED', 'PROCESSING') THEN 'PROCESSING'
        WHEN processing_status = 'FAILED' THEN 'FAILED'
        WHEN NOT neo4j_loaded AND NOT pinecone_loaded THEN 'NOT_LOADED'
        WHEN neo4j_loaded AND NOT pinecone_loaded THEN 'MISSING_VECTORS'
        WHEN NOT neo4j_loaded AND pinecone_loaded THEN 'MISSING_GRAPH'
        ELSE 'UNKNOWN'
    END AS consistency_status
FROM public.global_registry;

-- Add comments for the new columns
COMMENT ON COLUMN public.global_registry.processing_status IS 'Current processing status: NOT_STARTED, RESERVED, PROCESSING, COMPLETED, FAILED';
COMMENT ON COLUMN public.global_registry.processing_id IS 'Unique identifier for the process that reserved this document';
COMMENT ON COLUMN public.global_registry.reserved_at IS 'When this document was reserved for processing';

-- Create a function to clean up stale reservations
CREATE OR REPLACE FUNCTION cleanup_stale_reservations(max_age_minutes INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    cleanup_count INTEGER;
    cutoff_time TIMESTAMPTZ;
BEGIN
    cutoff_time := NOW() - INTERVAL '1 minute' * max_age_minutes;
    
    UPDATE public.global_registry 
    SET processing_status = 'FAILED',
        processing_id = NULL,
        reserved_at = NULL
    WHERE processing_status IN ('RESERVED', 'PROCESSING')
      AND reserved_at < cutoff_time;
    
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    
    RETURN cleanup_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION cleanup_stale_reservations IS 'Clean up stale document reservations that have been stuck in RESERVED or PROCESSING state';

-- Grant permissions for the new function
GRANT EXECUTE ON FUNCTION cleanup_stale_reservations TO authenticated;
