-- Global Registry table for ailex-be-ingest
-- Tracks the state of every legal document across all four stores:
-- G<PERSON> (raw files), Neo4j (graph), Pinecone (vectors), and this registry

-- Create the registry table
CREATE TABLE IF NOT EXISTS public.global_registry (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source TEXT NOT NULL CHECK (source IN ('vlaamse', 'eu_dump', 'eu_rest')),
    doc_id TEXT NOT NULL,
    gcs_uri TEXT,
    sha256 TEXT,
    neo4j_loaded BOOLEAN DEFAULT FALSE,
    pinecone_loaded BOOLEAN DEFAULT FALSE,
    article_count INTEGER DEFAULT 0,
    processing_status TEXT DEFAULT 'NOT_STARTED' CHECK (processing_status IN ('NOT_STARTED', 'RESERVED', 'PROCESSING', 'COMPLETED', 'FAILED')),
    processing_id TEXT,
    reserved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Ensure no duplicate documents per source
    CONSTRAINT unique_source_doc UNIQUE (source, doc_id)
);

-- Create indexes for common queries
CREATE INDEX IF NOT EXISTS idx_registry_source ON public.global_registry (source);
CREATE INDEX IF NOT EXISTS idx_registry_created_at ON public.global_registry (created_at);
CREATE INDEX IF NOT EXISTS idx_registry_consistency ON public.global_registry (neo4j_loaded, pinecone_loaded);
CREATE INDEX IF NOT EXISTS idx_registry_source_date ON public.global_registry (source, created_at);
CREATE INDEX IF NOT EXISTS idx_registry_processing_status ON public.global_registry (processing_status);
CREATE INDEX IF NOT EXISTS idx_registry_processing_id ON public.global_registry (processing_id);
CREATE INDEX IF NOT EXISTS idx_registry_reserved_at ON public.global_registry (reserved_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_registry_updated_at 
    BEFORE UPDATE ON public.global_registry 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE public.global_registry IS 'Global registry tracking the state of legal documents across all storage systems';
COMMENT ON COLUMN public.global_registry.id IS 'Unique identifier for this registry entry';
COMMENT ON COLUMN public.global_registry.source IS 'Source system: vlaamse, eu_dump, or eu_rest';
COMMENT ON COLUMN public.global_registry.doc_id IS 'Document identifier from source system (codexId or celex)';
COMMENT ON COLUMN public.global_registry.gcs_uri IS 'Google Cloud Storage URI for raw file';
COMMENT ON COLUMN public.global_registry.sha256 IS 'SHA-256 checksum of raw file content';
COMMENT ON COLUMN public.global_registry.neo4j_loaded IS 'True when document is successfully loaded into Neo4j';
COMMENT ON COLUMN public.global_registry.pinecone_loaded IS 'True when vectors are successfully loaded into Pinecone';
COMMENT ON COLUMN public.global_registry.article_count IS 'Number of articles/vectors for this document';
COMMENT ON COLUMN public.global_registry.processing_status IS 'Current processing status: NOT_STARTED, RESERVED, PROCESSING, COMPLETED, FAILED';
COMMENT ON COLUMN public.global_registry.processing_id IS 'Unique identifier for the process that reserved this document';
COMMENT ON COLUMN public.global_registry.reserved_at IS 'When this document was reserved for processing';
COMMENT ON COLUMN public.global_registry.created_at IS 'When this registry entry was first created';
COMMENT ON COLUMN public.global_registry.updated_at IS 'When this registry entry was last updated';

-- Create a view for consistency checking
CREATE OR REPLACE VIEW public.registry_consistency_view AS
SELECT
    id,
    source,
    doc_id,
    gcs_uri,
    sha256,
    neo4j_loaded,
    pinecone_loaded,
    article_count,
    processing_status,
    processing_id,
    reserved_at,
    created_at,
    updated_at,
    CASE
        WHEN processing_status = 'COMPLETED' AND neo4j_loaded AND pinecone_loaded THEN 'CONSISTENT'
        WHEN processing_status IN ('RESERVED', 'PROCESSING') THEN 'PROCESSING'
        WHEN processing_status = 'FAILED' THEN 'FAILED'
        WHEN NOT neo4j_loaded AND NOT pinecone_loaded THEN 'NOT_LOADED'
        WHEN neo4j_loaded AND NOT pinecone_loaded THEN 'MISSING_VECTORS'
        WHEN NOT neo4j_loaded AND pinecone_loaded THEN 'MISSING_GRAPH'
        ELSE 'UNKNOWN'
    END AS consistency_status
FROM public.global_registry;

COMMENT ON VIEW public.registry_consistency_view IS 'View showing consistency status of all registry entries';

-- Create function to get consistency stats
CREATE OR REPLACE FUNCTION get_consistency_stats(p_source TEXT DEFAULT NULL, p_since TIMESTAMPTZ DEFAULT NULL)
RETURNS TABLE (
    status TEXT,
    count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        consistency_status::TEXT,
        COUNT(*)::BIGINT
    FROM public.registry_consistency_view
    WHERE (p_source IS NULL OR source = p_source)
      AND (p_since IS NULL OR created_at >= p_since)
    GROUP BY consistency_status
    ORDER BY consistency_status;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION get_consistency_stats IS 'Get consistency statistics, optionally filtered by source and date';

-- Grant permissions (adjust as needed for your setup)
-- These are basic permissions - adjust based on your security requirements
GRANT SELECT, INSERT, UPDATE ON public.global_registry TO authenticated;
GRANT SELECT ON public.registry_consistency_view TO authenticated;
GRANT EXECUTE ON FUNCTION get_consistency_stats TO authenticated;
