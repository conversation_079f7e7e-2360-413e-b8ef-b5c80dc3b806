#!/usr/bin/env python3
"""
Clean the global registry for fresh testing.
"""

import sys
import os
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
load_dotenv()

def clean_registry(source: str = None):
    """
    Clean the global registry.
    
    Args:
        source: If provided, only clean entries for this source. If None, clean all.
    """
    try:
        from supabase import create_client
        
        supabase = create_client(
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_KEY')
        )
        
        print(f"🧹 CLEANING GLOBAL REGISTRY")
        print("=" * 40)
        
        if source:
            print(f"Cleaning entries for source: {source}")
            
            # Get entries to delete
            response = supabase.table('global_registry').select('id, doc_id').eq('source', source).execute()
            entries = response.data
            
            print(f"Found {len(entries)} entries to delete")
            
            # Delete entries
            if entries:
                for entry in entries:
                    supabase.table('global_registry').delete().eq('id', entry['id']).execute()
                    print(f"  Deleted: {entry['doc_id']}")
                
                # Verify deletion
                check_response = supabase.table('global_registry').select('id').eq('source', source).execute()
                remaining = len(check_response.data) if check_response.data else 0
                
                if remaining == 0:
                    print(f"✅ Successfully cleaned {len(entries)} entries for {source}")
                else:
                    print(f"⚠️  {remaining} entries still remain")
            else:
                print(f"✅ No entries found for {source}")
        else:
            print(f"Cleaning ALL registry entries")
            
            # Get all entries
            response = supabase.table('global_registry').select('id, source, doc_id').execute()
            entries = response.data
            
            print(f"Found {len(entries)} total entries to delete")
            
            # Delete all entries
            if entries:
                for entry in entries:
                    supabase.table('global_registry').delete().eq('id', entry['id']).execute()
                    print(f"  Deleted: {entry['source']}:{entry['doc_id']}")
                
                # Verify deletion
                check_response = supabase.table('global_registry').select('id').execute()
                remaining = len(check_response.data) if check_response.data else 0
                
                if remaining == 0:
                    print(f"✅ Successfully cleaned all {len(entries)} entries")
                else:
                    print(f"⚠️  {remaining} entries still remain")
            else:
                print(f"✅ Registry was already empty")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to clean registry: {e}")
        import traceback
        traceback.print_exc()
        return False

def clean_databases(source: str = None):
    """
    Clean Neo4j and Pinecone for fresh testing.
    
    Args:
        source: If provided, only clean data for this source. If None, clean all.
    """
    print(f"\n🧹 CLEANING DATABASES")
    print("=" * 40)
    
    try:
        # Clean Neo4j
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'), 
            auth=(os.getenv('NEO4J_USERNAME'), os.getenv('NEO4J_PASSWORD'))
        )
        
        with driver.session() as session:
            if source:
                # Delete acts and articles for specific source
                result = session.run(f"MATCH (a:Act) WHERE a.source = '{source}' DETACH DELETE a")
                result = session.run(f"MATCH (art:Article) WHERE art.id STARTS WITH '{source}_' DETACH DELETE art")
                print(f"✅ Cleaned Neo4j data for {source}")
            else:
                # Delete all acts and articles
                result = session.run("MATCH (a:Act) DETACH DELETE a")
                result = session.run("MATCH (art:Article) DETACH DELETE art")
                print(f"✅ Cleaned all Neo4j data")
        
        driver.close()
        
        # Clean Pinecone
        from pinecone import Pinecone

        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index(os.getenv('PINECONE_INDEX'))
        
        if source:
            # Map source to namespace
            namespace_map = {
                'vlaamse': 'vlaamse_codex',
                'eu_dump': 'eu_dump',
                'eu_rest': 'eu_rest'
            }
            namespace = namespace_map.get(source, source)
            
            # Delete all vectors in namespace
            try:
                index.delete(delete_all=True, namespace=namespace)
                print(f"✅ Cleaned Pinecone namespace: {namespace}")
            except Exception as e:
                if "Namespace not found" in str(e):
                    print(f"✅ Pinecone namespace {namespace} already empty (not found)")
                else:
                    raise
        else:
            # Delete all vectors in all namespaces
            for namespace in ['vlaamse_codex', 'eu_dump', 'eu_rest']:
                try:
                    index.delete(delete_all=True, namespace=namespace)
                    print(f"✅ Cleaned Pinecone namespace: {namespace}")
                except Exception as e:
                    if "Namespace not found" in str(e):
                        print(f"✅ Pinecone namespace {namespace} already empty (not found)")
                    else:
                        print(f"⚠️  Warning cleaning namespace {namespace}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to clean databases: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Clean registry and databases for fresh testing")
    parser.add_argument("--source", help="Source to clean (vlaamse, eu_dump, eu_rest). If not provided, cleans all.")
    parser.add_argument("--registry-only", action="store_true", help="Only clean registry, not databases")
    parser.add_argument("--databases-only", action="store_true", help="Only clean databases, not registry")
    
    args = parser.parse_args()
    
    success = True
    
    if not args.databases_only:
        success &= clean_registry(args.source)
    
    if not args.registry_only:
        success &= clean_databases(args.source)
    
    if success:
        print(f"\n🎉 CLEANUP COMPLETED SUCCESSFULLY!")
        sys.exit(0)
    else:
        print(f"\n❌ CLEANUP FAILED!")
        sys.exit(1)
