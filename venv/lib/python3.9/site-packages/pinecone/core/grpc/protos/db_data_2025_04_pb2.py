# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: db_data_2025-04.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'db_data_2025-04.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.api import field_behavior_pb2 as google_dot_api_dot_field__behavior__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x64\x62_data_2025-04.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\"J\n\x0cSparseValues\x12\x1d\n\x07indices\x18\x01 \x03(\rB\x03\xe0\x41\x02R\x07indices\x12\x1b\n\x06values\x18\x02 \x03(\x02\x42\x03\xe0\x41\x02R\x06values\"\x9e\x01\n\x06Vector\x12\x13\n\x02id\x18\x01 \x01(\tB\x03\xe0\x41\x02R\x02id\x12\x16\n\x06values\x18\x02 \x03(\x02R\x06values\x12\x32\n\rsparse_values\x18\x04 \x01(\x0b\x32\r.SparseValuesR\x0csparseValues\x12\x33\n\x08metadata\x18\x03 \x01(\x0b\x32\x17.google.protobuf.StructR\x08metadata\"\xba\x01\n\x0cScoredVector\x12\x13\n\x02id\x18\x01 \x01(\tB\x03\xe0\x41\x02R\x02id\x12\x14\n\x05score\x18\x02 \x01(\x02R\x05score\x12\x16\n\x06values\x18\x03 \x03(\x02R\x06values\x12\x32\n\rsparse_values\x18\x05 \x01(\x0b\x32\r.SparseValuesR\x0csparseValues\x12\x33\n\x08metadata\x18\x04 \x01(\x0b\x32\x17.google.protobuf.StructR\x08metadata\"\xa1\x01\n\x0cRequestUnion\x12(\n\x06upsert\x18\x01 \x01(\x0b\x32\x0e.UpsertRequestH\x00R\x06upsert\x12(\n\x06\x64\x65lete\x18\x02 \x01(\x0b\x32\x0e.DeleteRequestH\x00R\x06\x64\x65lete\x12(\n\x06update\x18\x03 \x01(\x0b\x32\x0e.UpdateRequestH\x00R\x06updateB\x13\n\x11RequestUnionInner\"U\n\rUpsertRequest\x12&\n\x07vectors\x18\x01 \x03(\x0b\x32\x07.VectorB\x03\xe0\x41\x02R\x07vectors\x12\x1c\n\tnamespace\x18\x02 \x01(\tR\tnamespace\"7\n\x0eUpsertResponse\x12%\n\x0eupserted_count\x18\x01 \x01(\rR\rupsertedCount\"\x8f\x01\n\rDeleteRequest\x12\x10\n\x03ids\x18\x01 \x03(\tR\x03ids\x12\x1d\n\ndelete_all\x18\x02 \x01(\x08R\tdeleteAll\x12\x1c\n\tnamespace\x18\x03 \x01(\tR\tnamespace\x12/\n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x17.google.protobuf.StructR\x06\x66ilter\"\x10\n\x0e\x44\x65leteResponse\"C\n\x0c\x46\x65tchRequest\x12\x15\n\x03ids\x18\x01 \x03(\tB\x03\xe0\x41\x02R\x03ids\x12\x1c\n\tnamespace\x18\x02 \x01(\tR\tnamespace\"\xd6\x01\n\rFetchResponse\x12\x35\n\x07vectors\x18\x01 \x03(\x0b\x32\x1b.FetchResponse.VectorsEntryR\x07vectors\x12\x1c\n\tnamespace\x18\x02 \x01(\tR\tnamespace\x12!\n\x05usage\x18\x03 \x01(\x0b\x32\x06.UsageH\x00R\x05usage\x88\x01\x01\x1a\x43\n\x0cVectorsEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x1d\n\x05value\x18\x02 \x01(\x0b\x32\x07.VectorR\x05value:\x02\x38\x01\x42\x08\n\x06_usage\"\xbd\x01\n\x0bListRequest\x12\x1b\n\x06prefix\x18\x01 \x01(\tH\x00R\x06prefix\x88\x01\x01\x12\x19\n\x05limit\x18\x02 \x01(\rH\x01R\x05limit\x88\x01\x01\x12.\n\x10pagination_token\x18\x03 \x01(\tH\x02R\x0fpaginationToken\x88\x01\x01\x12\x1c\n\tnamespace\x18\x04 \x01(\tR\tnamespaceB\t\n\x07_prefixB\x08\n\x06_limitB\x13\n\x11_pagination_token\" \n\nPagination\x12\x12\n\x04next\x18\x01 \x01(\tR\x04next\"\x1a\n\x08ListItem\x12\x0e\n\x02id\x18\x01 \x01(\tR\x02id\"\xbf\x01\n\x0cListResponse\x12#\n\x07vectors\x18\x01 \x03(\x0b\x32\t.ListItemR\x07vectors\x12\x30\n\npagination\x18\x02 \x01(\x0b\x32\x0b.PaginationH\x00R\npagination\x88\x01\x01\x12\x1c\n\tnamespace\x18\x03 \x01(\tR\tnamespace\x12!\n\x05usage\x18\x04 \x01(\x0b\x32\x06.UsageH\x01R\x05usage\x88\x01\x01\x42\r\n\x0b_paginationB\x08\n\x06_usage\"\xbd\x01\n\x0bQueryVector\x12\x16\n\x06values\x18\x01 \x03(\x02R\x06values\x12\x32\n\rsparse_values\x18\x05 \x01(\x0b\x32\r.SparseValuesR\x0csparseValues\x12\x13\n\x05top_k\x18\x02 \x01(\rR\x04topK\x12\x1c\n\tnamespace\x18\x03 \x01(\tR\tnamespace\x12/\n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x17.google.protobuf.StructR\x06\x66ilter\"\xd1\x02\n\x0cQueryRequest\x12\x1c\n\tnamespace\x18\x01 \x01(\tR\tnamespace\x12\x18\n\x05top_k\x18\x02 \x01(\rB\x03\xe0\x41\x02R\x04topK\x12/\n\x06\x66ilter\x18\x03 \x01(\x0b\x32\x17.google.protobuf.StructR\x06\x66ilter\x12%\n\x0einclude_values\x18\x04 \x01(\x08R\rincludeValues\x12)\n\x10include_metadata\x18\x05 \x01(\x08R\x0fincludeMetadata\x12*\n\x07queries\x18\x06 \x03(\x0b\x32\x0c.QueryVectorB\x02\x18\x01R\x07queries\x12\x16\n\x06vector\x18\x07 \x03(\x02R\x06vector\x12\x32\n\rsparse_vector\x18\t \x01(\x0b\x32\r.SparseValuesR\x0csparseVector\x12\x0e\n\x02id\x18\x08 \x01(\tR\x02id\"[\n\x12SingleQueryResults\x12\'\n\x07matches\x18\x01 \x03(\x0b\x32\r.ScoredVectorR\x07matches\x12\x1c\n\tnamespace\x18\x02 \x01(\tR\tnamespace\"\xb6\x01\n\rQueryResponse\x12\x31\n\x07results\x18\x01 \x03(\x0b\x32\x13.SingleQueryResultsB\x02\x18\x01R\x07results\x12\'\n\x07matches\x18\x02 \x03(\x0b\x32\r.ScoredVectorR\x07matches\x12\x1c\n\tnamespace\x18\x03 \x01(\tR\tnamespace\x12!\n\x05usage\x18\x04 \x01(\x0b\x32\x06.UsageH\x00R\x05usage\x88\x01\x01\x42\x08\n\x06_usage\":\n\x05Usage\x12\"\n\nread_units\x18\x01 \x01(\rH\x00R\treadUnits\x88\x01\x01\x42\r\n\x0b_read_units\"\xca\x01\n\rUpdateRequest\x12\x13\n\x02id\x18\x01 \x01(\tB\x03\xe0\x41\x02R\x02id\x12\x16\n\x06values\x18\x02 \x03(\x02R\x06values\x12\x32\n\rsparse_values\x18\x05 \x01(\x0b\x32\r.SparseValuesR\x0csparseValues\x12:\n\x0cset_metadata\x18\x03 \x01(\x0b\x32\x17.google.protobuf.StructR\x0bsetMetadata\x12\x1c\n\tnamespace\x18\x04 \x01(\tR\tnamespace\"\x10\n\x0eUpdateResponse\"L\n\x19\x44\x65scribeIndexStatsRequest\x12/\n\x06\x66ilter\x18\x01 \x01(\x0b\x32\x17.google.protobuf.StructR\x06\x66ilter\"5\n\x10NamespaceSummary\x12!\n\x0cvector_count\x18\x01 \x01(\rR\x0bvectorCount\"\x81\x01\n\x15ListNamespacesRequest\x12.\n\x10pagination_token\x18\x01 \x01(\tH\x00R\x0fpaginationToken\x88\x01\x01\x12\x19\n\x05limit\x18\x02 \x01(\rH\x01R\x05limit\x88\x01\x01\x42\x13\n\x11_pagination_tokenB\x08\n\x06_limit\"\x90\x01\n\x16ListNamespacesResponse\x12\x35\n\nnamespaces\x18\x01 \x03(\x0b\x32\x15.NamespaceDescriptionR\nnamespaces\x12\x30\n\npagination\x18\x02 \x01(\x0b\x32\x0b.PaginationH\x00R\npagination\x88\x01\x01\x42\r\n\x0b_pagination\"8\n\x18\x44\x65scribeNamespaceRequest\x12\x1c\n\tnamespace\x18\x01 \x01(\tR\tnamespace\"M\n\x14NamespaceDescription\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\x12!\n\x0crecord_count\x18\x02 \x01(\x04R\x0brecordCount\"6\n\x16\x44\x65leteNamespaceRequest\x12\x1c\n\tnamespace\x18\x01 \x01(\tR\tnamespace\"\x9f\x03\n\x1a\x44\x65scribeIndexStatsResponse\x12K\n\nnamespaces\x18\x01 \x03(\x0b\x32+.DescribeIndexStatsResponse.NamespacesEntryR\nnamespaces\x12!\n\tdimension\x18\x02 \x01(\rH\x00R\tdimension\x88\x01\x01\x12%\n\x0eindex_fullness\x18\x03 \x01(\x02R\rindexFullness\x12,\n\x12total_vector_count\x18\x04 \x01(\rR\x10totalVectorCount\x12\x1b\n\x06metric\x18\x05 \x01(\tH\x01R\x06metric\x88\x01\x01\x12$\n\x0bvector_type\x18\x06 \x01(\tH\x02R\nvectorType\x88\x01\x01\x1aP\n\x0fNamespacesEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\'\n\x05value\x18\x02 \x01(\x0b\x32\x11.NamespaceSummaryR\x05value:\x02\x38\x01\x42\x0c\n\n_dimensionB\t\n\x07_metricB\x0e\n\x0c_vector_type2\xd7\x06\n\rVectorService\x12\x45\n\x06Upsert\x12\x0e.UpsertRequest\x1a\x0f.UpsertResponse\"\x1a\x82\xd3\xe4\x93\x02\x14\"\x0f/vectors/upsert:\x01*\x12X\n\x06\x44\x65lete\x12\x0e.DeleteRequest\x1a\x0f.DeleteResponse\"-\x82\xd3\xe4\x93\x02\'\"\x0f/vectors/delete:\x01*Z\x11*\x0f/vectors/delete\x12>\n\x05\x46\x65tch\x12\r.FetchRequest\x1a\x0e.FetchResponse\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/vectors/fetch\x12:\n\x04List\x12\x0c.ListRequest\x1a\r.ListResponse\"\x15\x82\xd3\xe4\x93\x02\x0f\x12\r/vectors/list\x12\x39\n\x05Query\x12\r.QueryRequest\x1a\x0e.QueryResponse\"\x11\x82\xd3\xe4\x93\x02\x0b\"\x06/query:\x01*\x12\x45\n\x06Update\x12\x0e.UpdateRequest\x1a\x0f.UpdateResponse\"\x1a\x82\xd3\xe4\x93\x02\x14\"\x0f/vectors/update:\x01*\x12\x88\x01\n\x12\x44\x65scribeIndexStats\x12\x1a.DescribeIndexStatsRequest\x1a\x1b.DescribeIndexStatsResponse\"9\x82\xd3\xe4\x93\x02\x33\"\x15/describe_index_stats:\x01*Z\x17\x12\x15/describe_index_stats\x12V\n\x0eListNamespaces\x12\x16.ListNamespacesRequest\x1a\x17.ListNamespacesResponse\"\x13\x82\xd3\xe4\x93\x02\r\x12\x0b/namespaces\x12\x66\n\x11\x44\x65scribeNamespace\x12\x19.DescribeNamespaceRequest\x1a\x15.NamespaceDescription\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/namespaces/{namespace}\x12\\\n\x0f\x44\x65leteNamespace\x12\x17.DeleteNamespaceRequest\x1a\x0f.DeleteResponse\"\x1f\x82\xd3\xe4\x93\x02\x19*\x17/namespaces/{namespace}BS\n\x11io.pinecone.protoP\x01Z<github.com/pinecone-io/go-pinecone/internal/gen/db_data/grpcb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'db_data_2025_04_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\021io.pinecone.protoP\001Z<github.com/pinecone-io/go-pinecone/internal/gen/db_data/grpc'
  _globals['_SPARSEVALUES'].fields_by_name['indices']._loaded_options = None
  _globals['_SPARSEVALUES'].fields_by_name['indices']._serialized_options = b'\340A\002'
  _globals['_SPARSEVALUES'].fields_by_name['values']._loaded_options = None
  _globals['_SPARSEVALUES'].fields_by_name['values']._serialized_options = b'\340A\002'
  _globals['_VECTOR'].fields_by_name['id']._loaded_options = None
  _globals['_VECTOR'].fields_by_name['id']._serialized_options = b'\340A\002'
  _globals['_SCOREDVECTOR'].fields_by_name['id']._loaded_options = None
  _globals['_SCOREDVECTOR'].fields_by_name['id']._serialized_options = b'\340A\002'
  _globals['_UPSERTREQUEST'].fields_by_name['vectors']._loaded_options = None
  _globals['_UPSERTREQUEST'].fields_by_name['vectors']._serialized_options = b'\340A\002'
  _globals['_FETCHREQUEST'].fields_by_name['ids']._loaded_options = None
  _globals['_FETCHREQUEST'].fields_by_name['ids']._serialized_options = b'\340A\002'
  _globals['_FETCHRESPONSE_VECTORSENTRY']._loaded_options = None
  _globals['_FETCHRESPONSE_VECTORSENTRY']._serialized_options = b'8\001'
  _globals['_QUERYREQUEST'].fields_by_name['top_k']._loaded_options = None
  _globals['_QUERYREQUEST'].fields_by_name['top_k']._serialized_options = b'\340A\002'
  _globals['_QUERYREQUEST'].fields_by_name['queries']._loaded_options = None
  _globals['_QUERYREQUEST'].fields_by_name['queries']._serialized_options = b'\030\001'
  _globals['_QUERYRESPONSE'].fields_by_name['results']._loaded_options = None
  _globals['_QUERYRESPONSE'].fields_by_name['results']._serialized_options = b'\030\001'
  _globals['_UPDATEREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEREQUEST'].fields_by_name['id']._serialized_options = b'\340A\002'
  _globals['_DESCRIBEINDEXSTATSRESPONSE_NAMESPACESENTRY']._loaded_options = None
  _globals['_DESCRIBEINDEXSTATSRESPONSE_NAMESPACESENTRY']._serialized_options = b'8\001'
  _globals['_VECTORSERVICE'].methods_by_name['Upsert']._loaded_options = None
  _globals['_VECTORSERVICE'].methods_by_name['Upsert']._serialized_options = b'\202\323\344\223\002\024\"\017/vectors/upsert:\001*'
  _globals['_VECTORSERVICE'].methods_by_name['Delete']._loaded_options = None
  _globals['_VECTORSERVICE'].methods_by_name['Delete']._serialized_options = b'\202\323\344\223\002\'\"\017/vectors/delete:\001*Z\021*\017/vectors/delete'
  _globals['_VECTORSERVICE'].methods_by_name['Fetch']._loaded_options = None
  _globals['_VECTORSERVICE'].methods_by_name['Fetch']._serialized_options = b'\202\323\344\223\002\020\022\016/vectors/fetch'
  _globals['_VECTORSERVICE'].methods_by_name['List']._loaded_options = None
  _globals['_VECTORSERVICE'].methods_by_name['List']._serialized_options = b'\202\323\344\223\002\017\022\r/vectors/list'
  _globals['_VECTORSERVICE'].methods_by_name['Query']._loaded_options = None
  _globals['_VECTORSERVICE'].methods_by_name['Query']._serialized_options = b'\202\323\344\223\002\013\"\006/query:\001*'
  _globals['_VECTORSERVICE'].methods_by_name['Update']._loaded_options = None
  _globals['_VECTORSERVICE'].methods_by_name['Update']._serialized_options = b'\202\323\344\223\002\024\"\017/vectors/update:\001*'
  _globals['_VECTORSERVICE'].methods_by_name['DescribeIndexStats']._loaded_options = None
  _globals['_VECTORSERVICE'].methods_by_name['DescribeIndexStats']._serialized_options = b'\202\323\344\223\0023\"\025/describe_index_stats:\001*Z\027\022\025/describe_index_stats'
  _globals['_VECTORSERVICE'].methods_by_name['ListNamespaces']._loaded_options = None
  _globals['_VECTORSERVICE'].methods_by_name['ListNamespaces']._serialized_options = b'\202\323\344\223\002\r\022\013/namespaces'
  _globals['_VECTORSERVICE'].methods_by_name['DescribeNamespace']._loaded_options = None
  _globals['_VECTORSERVICE'].methods_by_name['DescribeNamespace']._serialized_options = b'\202\323\344\223\002\031\022\027/namespaces/{namespace}'
  _globals['_VECTORSERVICE'].methods_by_name['DeleteNamespace']._loaded_options = None
  _globals['_VECTORSERVICE'].methods_by_name['DeleteNamespace']._serialized_options = b'\202\323\344\223\002\031*\027/namespaces/{namespace}'
  _globals['_SPARSEVALUES']._serialized_start=118
  _globals['_SPARSEVALUES']._serialized_end=192
  _globals['_VECTOR']._serialized_start=195
  _globals['_VECTOR']._serialized_end=353
  _globals['_SCOREDVECTOR']._serialized_start=356
  _globals['_SCOREDVECTOR']._serialized_end=542
  _globals['_REQUESTUNION']._serialized_start=545
  _globals['_REQUESTUNION']._serialized_end=706
  _globals['_UPSERTREQUEST']._serialized_start=708
  _globals['_UPSERTREQUEST']._serialized_end=793
  _globals['_UPSERTRESPONSE']._serialized_start=795
  _globals['_UPSERTRESPONSE']._serialized_end=850
  _globals['_DELETEREQUEST']._serialized_start=853
  _globals['_DELETEREQUEST']._serialized_end=996
  _globals['_DELETERESPONSE']._serialized_start=998
  _globals['_DELETERESPONSE']._serialized_end=1014
  _globals['_FETCHREQUEST']._serialized_start=1016
  _globals['_FETCHREQUEST']._serialized_end=1083
  _globals['_FETCHRESPONSE']._serialized_start=1086
  _globals['_FETCHRESPONSE']._serialized_end=1300
  _globals['_FETCHRESPONSE_VECTORSENTRY']._serialized_start=1223
  _globals['_FETCHRESPONSE_VECTORSENTRY']._serialized_end=1290
  _globals['_LISTREQUEST']._serialized_start=1303
  _globals['_LISTREQUEST']._serialized_end=1492
  _globals['_PAGINATION']._serialized_start=1494
  _globals['_PAGINATION']._serialized_end=1526
  _globals['_LISTITEM']._serialized_start=1528
  _globals['_LISTITEM']._serialized_end=1554
  _globals['_LISTRESPONSE']._serialized_start=1557
  _globals['_LISTRESPONSE']._serialized_end=1748
  _globals['_QUERYVECTOR']._serialized_start=1751
  _globals['_QUERYVECTOR']._serialized_end=1940
  _globals['_QUERYREQUEST']._serialized_start=1943
  _globals['_QUERYREQUEST']._serialized_end=2280
  _globals['_SINGLEQUERYRESULTS']._serialized_start=2282
  _globals['_SINGLEQUERYRESULTS']._serialized_end=2373
  _globals['_QUERYRESPONSE']._serialized_start=2376
  _globals['_QUERYRESPONSE']._serialized_end=2558
  _globals['_USAGE']._serialized_start=2560
  _globals['_USAGE']._serialized_end=2618
  _globals['_UPDATEREQUEST']._serialized_start=2621
  _globals['_UPDATEREQUEST']._serialized_end=2823
  _globals['_UPDATERESPONSE']._serialized_start=2825
  _globals['_UPDATERESPONSE']._serialized_end=2841
  _globals['_DESCRIBEINDEXSTATSREQUEST']._serialized_start=2843
  _globals['_DESCRIBEINDEXSTATSREQUEST']._serialized_end=2919
  _globals['_NAMESPACESUMMARY']._serialized_start=2921
  _globals['_NAMESPACESUMMARY']._serialized_end=2974
  _globals['_LISTNAMESPACESREQUEST']._serialized_start=2977
  _globals['_LISTNAMESPACESREQUEST']._serialized_end=3106
  _globals['_LISTNAMESPACESRESPONSE']._serialized_start=3109
  _globals['_LISTNAMESPACESRESPONSE']._serialized_end=3253
  _globals['_DESCRIBENAMESPACEREQUEST']._serialized_start=3255
  _globals['_DESCRIBENAMESPACEREQUEST']._serialized_end=3311
  _globals['_NAMESPACEDESCRIPTION']._serialized_start=3313
  _globals['_NAMESPACEDESCRIPTION']._serialized_end=3390
  _globals['_DELETENAMESPACEREQUEST']._serialized_start=3392
  _globals['_DELETENAMESPACEREQUEST']._serialized_end=3446
  _globals['_DESCRIBEINDEXSTATSRESPONSE']._serialized_start=3449
  _globals['_DESCRIBEINDEXSTATSRESPONSE']._serialized_end=3864
  _globals['_DESCRIBEINDEXSTATSRESPONSE_NAMESPACESENTRY']._serialized_start=3743
  _globals['_DESCRIBEINDEXSTATSRESPONSE_NAMESPACESENTRY']._serialized_end=3823
  _globals['_VECTORSERVICE']._serialized_start=3867
  _globals['_VECTORSERVICE']._serialized_end=4722
# @@protoc_insertion_point(module_scope)
