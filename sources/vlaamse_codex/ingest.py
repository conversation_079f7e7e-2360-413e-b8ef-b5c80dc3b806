"""
Vlaamse Codex ingestion pipeline for ailex-be-ingest.

Complete pipeline that:
1. Fetches documents from Vlaamse Codex API
2. Downloads and parses XML content
3. Saves raw files to Google Cloud Storage
4. Upserts data to Neo4j knowledge graph
5. Creates vector embeddings and stores in Pinecone
"""

import os
import json
import hashlib
import logging
from datetime import date, datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Iterator
from dataclasses import dataclass

import tiktoken
import voyageai
from pinecone import Pinecone
from google.cloud import storage
from tenacity import retry, stop_after_attempt, wait_exponential

from common.config import config, setup_logging
from common.models import CommonAct, CommonArticle, PINECONE_NAMESPACES
from common.neo4j import Neo4jClient
from common.checkpoint import Checkpoint
from common.integrity import DataIntegrityValidator, IntegrityLevel
from common.transaction import TransactionalIngester, IngestionResult
from .client import VlaamseCodexClient
from .parse import VlaamseCodexParser

setup_logging()
logger = logging.getLogger(__name__)


@dataclass
class IngestionStats:
    """Statistics for ingestion process."""
    documents_processed: int = 0
    documents_failed: int = 0
    acts_created: int = 0
    articles_created: int = 0
    vectors_created: int = 0
    files_saved: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class VlaamseCodexIngester:
    """Complete ingestion pipeline for Vlaamse Codex documents with checkpointing."""

    def __init__(self,
                 dry_run: bool = False,
                 save_raw_files: bool = True,
                 resume: bool = True,
                 chunk_size: int = 1000,
                 chunk_overlap: int = 100):
        """
        Initialize the ingester.

        Args:
            dry_run: If True, don't write to databases/storage
            save_raw_files: Whether to save raw XML/PDF files to GCS
            resume: Whether to resume from checkpoint
            chunk_size: Token size for text chunks
            chunk_overlap: Overlap between chunks
        """
        self.dry_run = dry_run
        self.save_raw_files = save_raw_files
        self.resume = resume
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

        # Initialize components
        self.client = VlaamseCodexClient()
        self.parser = VlaamseCodexParser()
        self.stats = IngestionStats()

        # Initialize checkpoint
        self.checkpoint = Checkpoint('vlaamse')

        # Initialize transactional ingester
        self.transactional_ingester = TransactionalIngester(
            source='vlaamse',
            dry_run=dry_run,
            save_raw_files=save_raw_files,
            use_registry=False  # TEMPORARILY DISABLE registry for testing
        )
    
    def _setup_services(self):
        """Initialize external services."""
        # Voyage AI for embeddings
        self.voyage_client = voyageai.Client(api_key=config.VOYAGE_API_KEY)
        
        # Pinecone for vector storage
        pc = Pinecone(api_key=config.PINECONE_API_KEY)
        self.pinecone_index = pc.Index(config.PINECONE_INDEX)
        
        # Neo4j for knowledge graph
        self.neo4j_client = Neo4jClient()
        
        # Google Cloud Storage for raw files
        if self.save_raw_files and config.GCS_BUCKET:
            self.gcs_client = storage.Client()
            self.gcs_bucket = self.gcs_client.bucket(config.GCS_BUCKET)
        else:
            self.gcs_client = None
            self.gcs_bucket = None
    
    def ingest_documents(self,
                        cursor_date: Optional[date] = None,
                        limit: Optional[int] = None) -> IngestionStats:
        """
        Ingest documents from Vlaamse Codex API with checkpointing.

        Args:
            cursor_date: Start date for ingestion (overrides checkpoint)
            limit: Maximum number of documents to process

        Returns:
            IngestionStats with processing results
        """
        self.stats.start_time = datetime.now()
        logger.info(f"Starting Vlaamse Codex ingestion (dry_run={self.dry_run}, resume={self.resume})")

        # Determine starting cursor
        start_cursor = self._get_start_cursor(cursor_date)
        if start_cursor:
            logger.info(f"Starting from cursor: {start_cursor}")

        if limit:
            logger.info(f"Limited to {limit} documents")

        try:
            documents_processed = 0
            last_cursor = start_cursor

            # Fetch documents from API
            for doc_metadata in self.client.get_documents(cursor_date=start_cursor):
                if limit and documents_processed >= limit:
                    logger.info(f"Reached limit of {limit} documents")
                    break

                try:
                    # Process document atomically
                    result = self._process_document_atomic(doc_metadata)

                    if result.success:
                        self.stats.documents_processed += 1
                        self.stats.acts_created += 1
                        self.stats.articles_created += result.articles_count
                        self.stats.vectors_created += result.vectors_count
                        self.stats.files_saved += result.files_saved

                        # Update checkpoint after successful processing
                        last_cursor = self._extract_cursor(doc_metadata)
                        self._update_checkpoint(last_cursor, doc_metadata)

                        documents_processed += 1

                        if documents_processed % 10 == 0:
                            logger.info(f"Processed {documents_processed} documents...")
                    else:
                        logger.warning(f"Failed to process document {doc_metadata.get('Id', 'unknown')}: {result.error}")
                        self.stats.documents_failed += 1
                        # Continue processing instead of breaking for load test resilience
                        # Only break if we have too many consecutive failures
                        if self.stats.documents_failed > 50 and documents_processed == 0:
                            logger.error("Too many consecutive failures, stopping")
                            break

                except Exception as e:
                    logger.error(f"Unexpected error processing document {doc_metadata.get('Id', 'unknown')}: {e}")
                    self.stats.documents_failed += 1
                    # Continue processing instead of breaking for load test resilience
                    # Only break if we have too many consecutive failures
                    if self.stats.documents_failed > 50 and documents_processed == 0:
                        logger.error("Too many consecutive failures, stopping")
                        break

            self.stats.end_time = datetime.now()
            duration = self.stats.end_time - self.stats.start_time

            logger.info(f"Ingestion completed in {duration}")
            logger.info(f"Documents processed: {self.stats.documents_processed}")
            logger.info(f"Documents failed: {self.stats.documents_failed}")
            logger.info(f"Acts created: {self.stats.acts_created}")
            logger.info(f"Articles created: {self.stats.articles_created}")
            logger.info(f"Vectors created: {self.stats.vectors_created}")

            return self.stats

        except Exception as e:
            logger.error(f"Ingestion failed: {e}")
            raise
        finally:
            self._cleanup()
    
    def _get_start_cursor(self, cursor_date: Optional[date]) -> Optional[date]:
        """Get starting cursor from checkpoint or parameter."""
        if cursor_date:
            # Explicit cursor overrides checkpoint
            return cursor_date

        if not self.resume:
            # Not resuming, start fresh
            return None

        # Try to resume from checkpoint
        checkpoint_data = self.checkpoint.read()
        cursor_str = checkpoint_data.get('cursor')

        if cursor_str:
            try:
                from datetime import datetime
                return datetime.fromisoformat(cursor_str).date()
            except (ValueError, TypeError) as e:
                logger.warning(f"Invalid checkpoint cursor {cursor_str}: {e}")
                return None

        return None

    def _extract_cursor(self, doc_metadata: Dict[str, Any]) -> str:
        """Extract cursor value from document metadata."""
        # Use publication date as cursor
        pub_date = doc_metadata.get('DatumPublicatie')
        if pub_date:
            # Ensure ISO format
            if isinstance(pub_date, str):
                return pub_date
            else:
                return pub_date.isoformat()

        # Fallback to current timestamp
        return datetime.now().isoformat()

    def _update_checkpoint(self, cursor: str, doc_metadata: Dict[str, Any]):
        """Update checkpoint with current progress."""
        checkpoint_data = {
            'cursor': cursor,
            'last_document_id': doc_metadata.get('Id'),
            'last_numac': doc_metadata.get('NUMAC'),
            'documents_processed': self.stats.documents_processed,
            'last_updated': datetime.now().isoformat()
        }

        try:
            self.checkpoint.write(checkpoint_data)
        except Exception as e:
            logger.error(f"Failed to update checkpoint: {e}")
            # Don't fail the whole process for checkpoint errors

    def _process_document_atomic(self, doc_metadata: Dict[str, Any]) -> IngestionResult:
        """Process a single document atomically."""

        doc_id = doc_metadata.get('Id', 'unknown')
        logger.info(f"🔍 STEP 1: _process_document_atomic called for {doc_id}")

        # Check if document has content
        heeft_inhoud = doc_metadata.get('HeeftInhoud', False)
        logger.info(f"🔍 STEP 2: HeeftInhoud check for {doc_id}: {heeft_inhoud}")
        if not heeft_inhoud:
            logger.warning(f"Document {doc_id} has no content (HeeftInhoud=False)")
            return IngestionResult(success=False, act_id=doc_id, error="No content available")

        logger.info(f"🔍 STEP 3: Processing document {doc_id}")

        # Get full document content from /VolledigDocument endpoint
        logger.info(f"🔍 STEP 4: Getting volledig document for {doc_id}")
        volledig_document = self.client.get_volledig_document(doc_id)
        if not volledig_document:
            logger.info(f"🔍 STEP 4 FAILED: No volledig document for {doc_id}")
            return IngestionResult(success=False, act_id=doc_id, error="Failed to get full document")

        # Parse JSON to extract act and articles
        logger.info(f"🔍 STEP 5: Parsing document {doc_id}")
        try:
            act, articles = self.parser.parse_document(volledig_document, doc_metadata)
            logger.info(f"🔍 STEP 5 SUCCESS: Parsed {doc_id} -> {len(articles)} articles")
        except Exception as e:
            logger.info(f"🔍 STEP 5 FAILED: Parse failed for {doc_id}: {e}")
            return IngestionResult(success=False, act_id=doc_id, error=f"Parse failed: {e}")

        # Validate document completeness
        if not self.dry_run:
            validator = DataIntegrityValidator()
            try:
                integrity_issues = validator.validate_document_completeness(volledig_document, act, articles)
                critical_issues = [i for i in integrity_issues if i.level == IntegrityLevel.CRITICAL]
                if critical_issues:
                    logger.warning(f"Document completeness issues for {doc_id}: {[i.description for i in critical_issues]}")
                    # Continue processing but log issues for monitoring
            except Exception as e:
                logger.warning(f"Integrity validation failed for {doc_id}: {e}")
            finally:
                validator.close()

        # Use transactional ingester for atomic processing
        # Convert JSON dict back to string for storage
        import json
        raw_content_str = json.dumps(volledig_document, ensure_ascii=False, indent=2) if isinstance(volledig_document, dict) else volledig_document

        # Enhanced debug logging with print statements
        print(f"🔍 CALLING TransactionalIngester for {act.id}: {len(articles)} articles, raw_content_length={len(raw_content_str) if raw_content_str else 0}")
        print(f"🔍 TransactionalIngester config: dry_run={self.transactional_ingester.dry_run}, save_raw_files={self.transactional_ingester.save_raw_files}, use_registry={self.transactional_ingester.use_registry}")

        # Log article details
        for i, article in enumerate(articles):
            print(f"🔍 Article {i+1}: id={article.id}, number={article.number}, text_length={len(article.text) if article.text else 0}")

        result = self.transactional_ingester.ingest_document_atomic(
            act=act,
            articles=articles,
            raw_content=raw_content_str,  # Store the JSON content as string
            raw_metadata=doc_metadata
        )

        print(f"🔍 TransactionalIngester RESULT for {act.id}: success={result.success}, articles_count={result.articles_count}, vectors={result.vectors_count}, files={result.files_saved}, error={result.error}")
        return result
    
    def _save_raw_files(self, act: CommonAct, xml_content: str, metadata: Dict[str, Any]):
        """Save raw XML and PDF files to Google Cloud Storage."""
        if not self.gcs_bucket:
            return
        
        try:
            # Create file path: raw/vlaamse/{year}/{act_id}.xml
            year = act.date.year
            file_path = f"raw/vlaamse/{year}/{act.id}.xml"
            
            # Upload XML content
            blob = self.gcs_bucket.blob(file_path)
            blob.upload_from_string(xml_content, content_type='application/xml')
            
            logger.debug(f"Saved XML to GCS: {file_path}")
            self.stats.files_saved += 1
            
            # Also save metadata as JSON
            metadata_path = f"raw/vlaamse/{year}/{act.id}.metadata.json"
            metadata_blob = self.gcs_bucket.blob(metadata_path)
            metadata_blob.upload_from_string(
                json.dumps(metadata, indent=2, default=str),
                content_type='application/json'
            )
            
        except Exception as e:
            logger.error(f"Failed to save raw files for {act.id}: {e}")
    
    def _store_in_neo4j(self, act: CommonAct, articles: List[CommonArticle]):
        """Store act and articles in Neo4j knowledge graph."""
        try:
            # Upsert the act
            success = self.neo4j_client.upsert_act(act)
            if not success:
                logger.error(f"Failed to upsert act {act.id}")
                return
            
            # Upsert articles
            for article in articles:
                success = self.neo4j_client.upsert_article(article)
                if not success:
                    logger.error(f"Failed to upsert article {article.id}")
            
            logger.debug(f"Stored in Neo4j: {act.id} with {len(articles)} articles")
            
        except Exception as e:
            logger.error(f"Failed to store in Neo4j: {e}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=2, max=60)
    )
    def _create_embeddings(self, act: CommonAct, articles: List[CommonArticle]):
        """Create and store vector embeddings for articles."""
        if not articles:
            return
        
        try:
            # Chunk article texts
            all_chunks = []
            chunk_metadata = []
            
            for article in articles:
                chunks = self._chunk_text(article.text)
                for i, chunk in enumerate(chunks):
                    all_chunks.append(chunk)
                    chunk_metadata.append({
                        'act_id': act.id,
                        'article_id': article.id,
                        'article_number': article.number,
                        'chunk_index': i,
                        'language': article.language,
                        'source': act.source,
                        'title': act.title,
                        'date': act.date.isoformat(),
                        'eli': act.eli,
                        'text_preview': chunk[:200]
                    })
            
            if not all_chunks:
                return
            
            # Create embeddings
            result = self.voyage_client.embed(
                texts=all_chunks,
                model=config.VOYAGE_MODEL,
                output_dimension=config.VOYAGE_DIMENSION
            )
            
            # Prepare vectors for Pinecone
            vectors = []
            namespace = PINECONE_NAMESPACES[act.source]
            
            for chunk, embedding, metadata in zip(all_chunks, result.embeddings, chunk_metadata):
                vector_id = hashlib.md5(chunk.encode()).hexdigest()
                vectors.append({
                    'id': vector_id,
                    'values': embedding,
                    'metadata': metadata
                })
            
            # Upsert to Pinecone
            self.pinecone_index.upsert(vectors=vectors, namespace=namespace)
            
            self.stats.vectors_created += len(vectors)
            logger.debug(f"Created {len(vectors)} embeddings for {act.id}")
            
        except Exception as e:
            logger.error(f"Failed to create embeddings for {act.id}: {e}")
            raise
    
    def _chunk_text(self, text: str) -> List[str]:
        """Split text into overlapping chunks."""
        if not text or len(text.strip()) < 50:
            return []
        
        tokens = self.tokenizer.encode(text)
        chunks = []
        
        for i in range(0, len(tokens), self.chunk_size - self.chunk_overlap):
            chunk_tokens = tokens[i:i + self.chunk_size]
            if len(chunk_tokens) < 50:  # Skip very small chunks
                continue
            chunk_text = self.tokenizer.decode(chunk_tokens)
            chunks.append(chunk_text)
        
        return chunks
    
    def reset_checkpoint(self):
        """Reset checkpoint to start fresh."""
        try:
            self.checkpoint.delete()
            logger.info("Checkpoint reset successfully")
        except Exception as e:
            logger.error(f"Failed to reset checkpoint: {e}")
            raise

    def get_checkpoint_status(self) -> Dict[str, Any]:
        """Get current checkpoint status."""
        return self.checkpoint.get_stats()

    def _cleanup(self):
        """Clean up resources."""
        if hasattr(self, 'client'):
            self.client.close()
        if hasattr(self, 'transactional_ingester'):
            self.transactional_ingester.close()


def main():
    """Main function for command-line usage."""
    import argparse

    parser = argparse.ArgumentParser(description="Ingest Vlaamse Codex documents with checkpointing")
    parser.add_argument("--dry-run", action="store_true", help="Don't write to databases")
    parser.add_argument("--limit", type=int, help="Limit number of documents")
    parser.add_argument("--since", type=str, help="Start date (YYYY-MM-DD)")
    parser.add_argument("--no-raw-files", action="store_true", help="Don't save raw files to GCS")
    parser.add_argument("--resume", action="store_true", default=True, help="Resume from checkpoint (default: True)")
    parser.add_argument("--no-resume", dest="resume", action="store_false", help="Don't resume from checkpoint")
    parser.add_argument("--reset", action="store_true", help="Reset checkpoint and start fresh")
    parser.add_argument("--status", action="store_true", help="Show checkpoint status and exit")

    args = parser.parse_args()

    # Create ingester
    ingester = VlaamseCodexIngester(
        dry_run=args.dry_run,
        save_raw_files=not args.no_raw_files,
        resume=args.resume
    )

    # Handle status check
    if args.status:
        status = ingester.get_checkpoint_status()
        print("📊 Checkpoint Status:")
        for key, value in status.items():
            print(f"   {key}: {value}")
        return

    # Handle reset
    if args.reset:
        print("🔄 Resetting checkpoint...")
        ingester.reset_checkpoint()
        print("✅ Checkpoint reset successfully")
        if not args.limit and not args.since:
            print("💡 Use --limit or --since to start ingestion after reset")
            return

    # Parse date
    cursor_date = None
    if args.since:
        cursor_date = datetime.strptime(args.since, '%Y-%m-%d').date()

    # Show current status
    if args.resume and not args.reset:
        status = ingester.get_checkpoint_status()
        if status['exists']:
            print(f"📍 Resuming from checkpoint: {status['cursor']}")
        else:
            print("🆕 No checkpoint found, starting fresh")

    # Run ingestion
    try:
        stats = ingester.ingest_documents(
            cursor_date=cursor_date,
            limit=args.limit
        )

        print(f"✅ Ingestion completed: {stats.documents_processed} documents processed")
        if stats.documents_failed > 0:
            print(f"⚠️  {stats.documents_failed} documents failed")

    except KeyboardInterrupt:
        print("\n🛑 Ingestion interrupted by user")
        print("💾 Progress has been saved to checkpoint")
        print("🔄 Run again to resume from where you left off")
    except Exception as e:
        print(f"❌ Ingestion failed: {e}")
        print("💾 Progress has been saved to checkpoint")
        print("🔄 Run again to resume from where you left off")


if __name__ == "__main__":
    main()
