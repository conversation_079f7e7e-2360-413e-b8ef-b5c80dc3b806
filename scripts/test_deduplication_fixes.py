#!/usr/bin/env python3
"""
Test script to verify registry deduplication fixes work correctly.

This script tests the new atomic reservation system and verifies that
duplicate document processing is properly prevented.
"""

import sys
import uuid
import time
from datetime import datetime, date
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config, setup_logging
from common.registry import GlobalRegistry, RegistryEntry, calculate_sha256
from common.models import CommonAct, CommonArticle

setup_logging()


def test_atomic_reservation():
    """Test the atomic reservation system."""
    print("🔍 Testing Atomic Reservation System...")
    
    try:
        # Create registry (with auto-cleanup disabled for testing)
        registry = GlobalRegistry(auto_cleanup=False)
        
        # Test document details
        source = "vlaamse"
        doc_id = f"test_doc_{uuid.uuid4().hex[:8]}"
        content = "Test document content for deduplication testing"
        sha256 = calculate_sha256(content)
        
        print(f"📄 Test document: {source}:{doc_id}")
        print(f"🔐 SHA-256: {sha256}")
        
        # Test 1: First reservation should succeed
        print("\n🔍 Test 1: First reservation attempt...")
        processing_id_1 = str(uuid.uuid4())
        result_1 = registry.try_reserve_document(source, doc_id, sha256, processing_id_1)
        print(f"✅ First reservation result: {result_1}")
        assert result_1 is True, "First reservation should succeed"
        
        # Test 2: Second reservation should fail (document already reserved)
        print("\n🔍 Test 2: Second reservation attempt (should fail)...")
        processing_id_2 = str(uuid.uuid4())
        result_2 = registry.try_reserve_document(source, doc_id, sha256, processing_id_2)
        print(f"✅ Second reservation result: {result_2}")
        assert result_2 is False, "Second reservation should fail"
        
        # Test 3: Update to PROCESSING status
        print("\n🔍 Test 3: Update to PROCESSING status...")
        update_result = registry.update_processing_status(source, doc_id, "PROCESSING", processing_id_1)
        print(f"✅ Processing status update result: {update_result}")
        assert update_result is True, "Status update should succeed"
        
        # Test 4: Third reservation should still fail (document being processed)
        print("\n🔍 Test 4: Third reservation attempt (should still fail)...")
        processing_id_3 = str(uuid.uuid4())
        result_3 = registry.try_reserve_document(source, doc_id, sha256, processing_id_3)
        print(f"✅ Third reservation result: {result_3}")
        assert result_3 is False, "Third reservation should fail"
        
        # Test 5: Complete processing
        print("\n🔍 Test 5: Complete processing...")
        registry.update_flags(source, doc_id, neo4j_loaded=True, pinecone_loaded=True)
        complete_result = registry.update_processing_status(source, doc_id, "COMPLETED", processing_id_1)
        print(f"✅ Completion result: {complete_result}")
        
        # Test 6: Fourth reservation should fail (document completed)
        print("\n🔍 Test 6: Fourth reservation attempt (should fail - completed)...")
        processing_id_4 = str(uuid.uuid4())
        result_4 = registry.try_reserve_document(source, doc_id, sha256, processing_id_4)
        print(f"✅ Fourth reservation result: {result_4}")
        assert result_4 is False, "Fourth reservation should fail"
        
        # Test 7: exists() method should return True
        print("\n🔍 Test 7: exists() method check...")
        exists_result = registry.exists(source, doc_id, sha256)
        print(f"✅ exists() result: {exists_result}")
        assert exists_result is True, "exists() should return True for completed document"
        
        print("\n✅ All atomic reservation tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Atomic reservation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_sha256_mismatch_reprocessing():
    """Test that SHA-256 mismatch allows reprocessing."""
    print("\n🔍 Testing SHA-256 Mismatch Reprocessing...")
    
    try:
        registry = GlobalRegistry(auto_cleanup=False)
        
        source = "vlaamse"
        doc_id = f"test_doc_sha_{uuid.uuid4().hex[:8]}"
        
        # First content and processing
        content_1 = "Original document content"
        sha256_1 = calculate_sha256(content_1)
        processing_id_1 = str(uuid.uuid4())
        
        print(f"📄 Test document: {source}:{doc_id}")
        print(f"🔐 Original SHA-256: {sha256_1}")
        
        # Reserve and complete first version
        result_1 = registry.try_reserve_document(source, doc_id, sha256_1, processing_id_1)
        assert result_1 is True, "First reservation should succeed"
        
        registry.update_processing_status(source, doc_id, "PROCESSING", processing_id_1)
        registry.update_flags(source, doc_id, neo4j_loaded=True, pinecone_loaded=True)
        registry.update_processing_status(source, doc_id, "COMPLETED", processing_id_1)
        
        print("✅ First version completed")
        
        # Second content (different SHA-256)
        content_2 = "Updated document content with changes"
        sha256_2 = calculate_sha256(content_2)
        processing_id_2 = str(uuid.uuid4())
        
        print(f"🔐 Updated SHA-256: {sha256_2}")
        
        # Should allow reprocessing due to SHA-256 mismatch
        result_2 = registry.try_reserve_document(source, doc_id, sha256_2, processing_id_2)
        print(f"✅ Reprocessing reservation result: {result_2}")
        assert result_2 is True, "Reprocessing should be allowed for different SHA-256"
        
        print("✅ SHA-256 mismatch reprocessing test passed!")
        return True
        
    except Exception as e:
        print(f"❌ SHA-256 mismatch test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_stale_reservation_cleanup():
    """Test cleanup of stale reservations."""
    print("\n🔍 Testing Stale Reservation Cleanup...")
    
    try:
        registry = GlobalRegistry(auto_cleanup=False)
        
        # Create a test reservation that we'll mark as stale
        source = "vlaamse"
        doc_id = f"test_stale_{uuid.uuid4().hex[:8]}"
        sha256 = calculate_sha256("test content")
        processing_id = str(uuid.uuid4())
        
        print(f"📄 Test document: {source}:{doc_id}")
        
        # Reserve document
        result = registry.try_reserve_document(source, doc_id, sha256, processing_id)
        assert result is True, "Reservation should succeed"
        
        print("✅ Document reserved")
        
        # Manually update reserved_at to make it stale (simulate old reservation)
        from datetime import datetime, timedelta
        old_time = datetime.now() - timedelta(hours=2)
        
        # Update the reservation to be stale
        registry.client.table(registry.table_name).update({
            "reserved_at": old_time.isoformat()
        }).eq("source", source).eq("doc_id", doc_id).execute()
        
        print("✅ Reservation marked as stale")
        
        # Run cleanup
        cleaned_count = registry.cleanup_stale_reservations(max_age_minutes=30)
        print(f"✅ Cleaned up {cleaned_count} stale reservations")
        
        # Verify document can now be reserved again
        new_processing_id = str(uuid.uuid4())
        new_result = registry.try_reserve_document(source, doc_id, sha256, new_processing_id)
        print(f"✅ New reservation after cleanup: {new_result}")
        assert new_result is True, "Should be able to reserve after cleanup"
        
        print("✅ Stale reservation cleanup test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Stale reservation cleanup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all deduplication tests."""
    print("🚀 Starting Registry Deduplication Tests...")
    print("=" * 60)
    
    tests = [
        test_atomic_reservation,
        test_sha256_mismatch_reprocessing,
        test_stale_reservation_cleanup
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All deduplication tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
