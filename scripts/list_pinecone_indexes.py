#!/usr/bin/env python3
"""
List existing Pinecone indexes and their stats.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from pinecone import Pinecone
from common.config import config


def list_pinecone_indexes():
    """List all Pinecone indexes and their stats."""
    print("🔍 Listing Pinecone indexes...")
    
    try:
        pc = Pinecone(api_key=config.PINECONE_API_KEY)

        # List all indexes
        indexes = pc.list_indexes()
        
        if not indexes:
            print("📭 No indexes found")
            return
        
        print(f"📊 Found {len(indexes)} indexes:")
        print()
        
        for i, idx in enumerate(indexes, 1):
            print(f"{i}. {idx.name}")
            print(f"   📏 Dimension: {idx.dimension}")
            print(f"   📐 Metric: {idx.metric}")
            print(f"   ☁️  Spec: {idx.spec}")
            
            # Get detailed stats
            try:
                index = pc.Index(idx.name)
                stats = index.describe_index_stats()
                print(f"   📊 Total vectors: {stats.total_vector_count}")
                
                if stats.namespaces:
                    print(f"   📂 Namespaces:")
                    for ns_name, ns_stats in stats.namespaces.items():
                        print(f"      - {ns_name}: {ns_stats.vector_count} vectors")
                else:
                    print(f"   📂 Namespaces: None")
                    
            except Exception as e:
                print(f"   ❌ Could not get stats: {e}")
            
            print()
        
        # Check if our target index exists
        target_index = config.PINECONE_INDEX
        existing_names = [idx.name for idx in indexes]
        
        if target_index in existing_names:
            print(f"✅ Target index '{target_index}' already exists!")
        else:
            print(f"❌ Target index '{target_index}' does not exist")
            print(f"💡 You have {len(indexes)}/5 indexes (Starter plan limit)")
            
            if len(indexes) >= 5:
                print("⚠️  You've reached the 5-index limit for Starter plan")
                print("💡 Options:")
                print("   1. Delete an unused index")
                print("   2. Use an existing index with namespaces")
                print("   3. Upgrade to a paid Pinecone plan")
        
    except Exception as e:
        print(f"❌ Failed to list indexes: {e}")


def suggest_solutions():
    """Suggest solutions for the index limit issue."""
    print("🔧 Suggested Solutions:")
    print("=" * 30)
    print()
    print("1. 🗑️  Delete unused indexes:")
    print("   python scripts/delete_pinecone_index.py <index_name>")
    print()
    print("2. 📂 Use existing index with namespaces:")
    print("   - Update PINECONE_INDEX in .env to an existing index")
    print("   - Use namespaces to separate data (e.g., 'vlaamse_codex')")
    print()
    print("3. 💰 Upgrade Pinecone plan:")
    print("   - Visit https://app.pinecone.io/")
    print("   - Upgrade to Standard plan for more indexes")
    print()
    print("4. 🔄 Reuse existing index:")
    print("   - If you have an index with 1024 dimensions and cosine metric")
    print("   - Just update the PINECONE_INDEX environment variable")


def main():
    """Main function."""
    print("📋 Pinecone Index Analysis")
    print("=" * 50)
    
    if not config.PINECONE_API_KEY:
        print("❌ PINECONE_API_KEY not set")
        return False
    
    list_pinecone_indexes()
    print()
    suggest_solutions()
    
    return True


if __name__ == "__main__":
    main()
